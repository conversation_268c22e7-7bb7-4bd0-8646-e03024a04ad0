package com.lmig.uscm.booktransfer.opportunity.config;

import com.lmig.uscm.booktransfer.opportunity.config.properties.AddressCleanseServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.AuditLogRestApiOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.BTPaymentServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.CustomerAccountWebClientConfigProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.PropertyInfoWebClientConfigProperties;
import com.lmig.uscm.booktransfer.opportunity.config.properties.QuoteAdapterOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.QuoteReportOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.QuotingGuidelineOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.SensitiveDataServiceOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.UploadPreprocessorOauth2ClientDetails;
import com.lmig.uscm.booktransfer.opportunity.config.properties.EFTPaymentAccountsOauth2ClientDetails;
import com.lmig.usconsumermarkets.booktransfer.security.starter.WebClientUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2RestTemplate;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Arrays;


@Configuration
public class WebConfig {

	@Value("${service.book-transfer-url-provider.base-url}")
	String bookTransferServiceUrl;

	@Bean("emailClient")
	public WebClient emailServiceWebClient() {
		return WebClientUtil.getWebClientWithNoAuth("emailClient");
	}

	@Bean("bookTransferServiceTemplate")
	public RestTemplate bookTransferServiceTemplate() {
		return new RestTemplate(new HttpComponentsClientHttpRequestFactory());
	}

	@Bean("bookTransferServiceWebClient")
	public WebClient getBookTransferServiceWebClient() {
		return WebClientUtil.getWebClientWithNoAuth("BookTransferService", bookTransferServiceUrl)
				.mutate()
				.exchangeStrategies(ExchangeStrategies
						.builder()
						.codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(16*1024*1024))
						.build())
				.build();
	}

	@Bean("processResultWebClient")
	public WebClient processResultServiceWebClient() {
		return WebClientUtil.getWebClientWithNoAuth("processResultWebClient");
	}

	/**
	 * this is the bean that we are creating for quote adapter use in this project
	 */
	@Bean("quotingAdapterWebClient")
	public WebClient quotingAdapterWebClient(QuoteAdapterOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("quotingAdapter", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	@Bean("sensitiveDataTemplate")
	public RestTemplate sensitiveDataTemplate(final SensitiveDataServiceOauth2ClientDetails resource) {
		return new OAuth2RestTemplate(resource);
	}

	/**
	 * Creates webclient bean with OAuth2 client credentials
	 * <a href="https://www.baeldung.com/spring-webclient-oauth2">spring-webclient-oauth2</a>
	 * <a href="https://manhtai.github.io/posts/spring-webclient-oauth2-client-credentials/">spring-webclient-oauth2-client-credentials</a>
	 */
	@Bean("auditLogClient")
	public WebClient auditLogWebClient(final AuditLogRestApiOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("auditlog", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope())
				.mutate()
				.clientConnector(new ReactorClientHttpConnector(WebClientUtil.getHttpClient("auditlog", 30000)))
				.exchangeStrategies(ExchangeStrategies
						.builder()
						.codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(16*1024*1024))
						.build())
				.build();
	}

	@Bean("uploadPreprocessorClient")
	public WebClient uploadPreprocessotWebClient(final UploadPreprocessorOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("uploadpreprocessor", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	@Bean("sensitiveDataWebClient")
	public WebClient sensitiveDataWebClient(final SensitiveDataServiceOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("sensitivedata", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	/**
	 * Creates webclient bean with OAuth2 client credentials for QuotingGuideline service
	 * <a href="https://www.baeldung.com/spring-webclient-oauth2">spring-webclient-oauth2</a>
	 * <a href="https://manhtai.github.io/posts/spring-webclient-oauth2-client-credentials/">spring-webclient-oauth2-client-credentials</a>
	 * @return Webclient
	 */
	@Bean("quotingGuidelineClient")
	public WebClient quotingGuidelineWebClient(final QuotingGuidelineOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("quotingGuideline", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	@Bean("paymentServiceClient")
	public WebClient paymentService(final BTPaymentServiceOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("paymentService", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	@Bean("addressCleanseClient")
	public WebClient addressCleanse(final AddressCleanseServiceOauth2ClientDetails resource) {
		return WebClientUtil.getWebClientWithOAuth("addressCleanse", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope())
				.mutate()
				.exchangeStrategies(ExchangeStrategies
						.builder()
						.codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(16*1024*1024))
						.build())
				.build();
	}

	@Bean("eftPaymentAccountsWebClient")
	public WebClient eftPaymentAccounts(final EFTPaymentAccountsOauth2ClientDetails resource){
		return WebClientUtil.getWebClientWithOAuth("eftPaymentAccounts", resource.getAccessTokenUri(), resource.getClientId(),
				resource.getClientSecret(), resource.getGrantType(), resource.getScope());
	}

	@Bean("quoteReportWebClient")
	public WebClient quoteReportService(ClientRegistrationRepository clientRegistrationRepository,
										OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
										QuoteReportOauth2ClientDetails webClientConfigProperties) {
		return WebClientUtil.getWebClient(clientRegistrationRepository, oAuth2AuthorizedClientService,
				webClientConfigProperties, "quote-report");
	}

	@Bean("customerAccountWebClient")
	@Profile("!unit")
	public WebClient customerAccountWebClient(ClientRegistrationRepository clientRegistrationRepository,
		OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
		CustomerAccountWebClientConfigProperties webClientConfigProperties) {
		return WebClientUtil.getWebClient(clientRegistrationRepository, oAuth2AuthorizedClientService,
			webClientConfigProperties, "customerAccount");
	}

	@Bean("propertyInfoClient")
	@Profile("!unit")
	public WebClient propertyInfoClient(ClientRegistrationRepository clientRegistrationRepository,
																						OAuth2AuthorizedClientService oAuth2AuthorizedClientService,
																						PropertyInfoWebClientConfigProperties webClientConfigProperties) {
		return WebClientUtil.getWebClient(clientRegistrationRepository, oAuth2AuthorizedClientService,
			webClientConfigProperties, "property-info");
	}

	/**
	 * This class builds out CORS security and enables Cross Origin with the security config for all origins.
	 */
	@Bean
	public CorsConfigurationSource corsConfigurationSource() {
		CorsConfiguration configuration = new CorsConfiguration();
		configuration.addAllowedOriginPattern(CorsConfiguration.ALL);
		configuration.setAllowedMethods(Arrays.asList("OPTIONS", "GET", "POST", "PUT", "PATCH", "DELETE"));
		configuration.setAllowCredentials(true);
		configuration.setAllowedHeaders(Arrays.asList("authorization", "Content-Type", "x-requested-with"));
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", configuration);
		return source;
	}
}
