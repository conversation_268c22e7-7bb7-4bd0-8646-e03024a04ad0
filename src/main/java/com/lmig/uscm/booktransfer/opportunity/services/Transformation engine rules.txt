Full Grammar specification
Below is the full grammar for the Transformation Engine programming language.

The grammar uses the following BNF-style conventions:

[x] denotes zero or one occurrences of x.
x* denotes zero or more occurrences of x.
x+ denotes one or more occurrences of x.
(x | y) means one of either x or y.
Other notes:

Miscellaneous keywords are always optional.
This grammar is not the implementation reference. See this lucid chart for the original reference.
# STARTING RULES
# ==============

               <package> ::= <package>* <filter>* <rule>*
                <filter> ::= <rule>
                  <rule> ::= <rule>* <rule_statement> 
        <rule_statement> ::= <rule_statement>* (<logical_expression> | <collection_expression> | <expression>)

          <search_input> ::= (<keyword> | <logical_expression> | <collection_expression> | <expression>)*
                           
# EXPRESSIONS
# ===========

    <logical_expression> ::= <if_operation>
                           | <else_operation>
                           | <and_clause>
                           | <or_clause>
 <collection_expression> ::= <for_each_operation>
                           | <for_all_operation>

            <expression> ::= <set_operation>
                           | <remove_operation>
                           | <delete_operation>
                           | <create_operation>
                           | <format_operation>
                           | <rename_operation>
                           | <move_operation>
                           | <replace_operation>
                           | ANY <disjunction>
                           | <disjunction>
           <disjunction> ::= <conjunction> (OR <conjunction>)+
                           | <conjunction>
           <conjunction> ::= <inversion> (AND <inversion>)+
                           | <inversion>
             <inversion> ::= <comparison> (IS_NOT <comparison>)+
                           | <comparison>
            <comparison> ::= <sum> <comparison_keyword> <sum>
                           | <sum>
                   <sum> ::= <term> PLUS <term>
                           | <term> MINUS <term>
                           | <term>
                  <term> ::= <term> MULTIPLY <primary>
                           | <term> DIVIDE <primary>
                           | <primary>
               <primary> ::= <function_keyword> <atom>
                           | <atom>
                  <atom> ::= <value_selector>
                           | <group>
                 <group> ::= '(' <expression> ')'

# OPERATIONS
# ==========          
 
          <if_operation> ::= IF <expression> 
        <else_operation> ::= ELSE <expression>
            <and_clause> ::= AND <conditional_expression>
             <or_clause> ::= OR <conditional_expression>

    <for_each_operation> ::= FOR_EACH XPATH_MAPPING
     <for_all_operation> ::= FOR_ALL XPATH_MAPPING
 
         <set_operation> ::= SET XPATH_MAPPING [EQUAL_TO] <expression>
      <remove_operation> ::= REMOVE <expression> [FROM] XPATH_MAPPING
      <delete_operation> ::= DELETE XPATH_MAPPING
      <create_operation> ::= CREATE XPATH_MAPPING
      <format_operation> ::= FORMAT XPATH_MAPPING <value_selector>
      <rename_operation> ::= RENAME XPATH_MAPPING [AS] <value_selector>
        <move_operation> ::= MOVE XPATH_MAPPING [IN] XPATH_MAPPING
     <replace_operation> ::= REPLACE <value_selector> [IN] XPATH_MAPPING [WITH] <value_selector>

# SELECTORS
# =========
 
        <value_selector> ::= REGEX_MAPPING
                           | STRING <data_type>
             <data_type> ::= '(as text)'
                           | '(as number)'
                           | '(as decimal)'
                           | '(as date)'
                           | '(as year)'
                           | '(as month)'
                           | '(as day)'
                           | '(as zip code)'
                           | '(as phone number)'
                           | '(as currency)'
                           | '(as significant)'
                           | '(as regex)'
                           | '(as upper case)'
                           | '(as lower case)'
                           | '(as name case)'
 
# KEYWORDS
# ========

               <keyword> ::= <action_keyword>
                           | <comparison_keyword>
                           | <logical_keyword>
                           | <computation_keyword>
                           | <function_keyword>
                           | <miscellaneous_keyword>
                           | <collection_keyword>
                           | <precedence_keyword>
        <action_keyword> ::= SET
                           | REMOVE
                           | DELETE
                           | CREATE
                           | FORMAT
                           | RENAME
                           | MOVE
                           | REPLACE
    <comparison_keyword> ::= IS_EQUAL_TO
                           | CONTAINS
                           | IS_GREATER_THAN
                           | IS_LESS_THAN
                           | IS_IN_LIST
                           | BETWEEN
                           | IS_MISSING
       <logical_keyword> ::= IF
                           | OR
                           | ANY
                           | AND
                           | ALL
                           | IS_NOT
                           | ELSE
   <computation_keyword> ::= PLUS
                           | MINUS
                           | DIVIDE
                           | MULTIPLY
      <function_keyword> ::= ROUND
                           | HIGHEST
                           | LOWEST
                           | COMBINE
 <miscellaneous_keyword> ::= EQUAL_TO
                           | AS
                           | FROM
                           | IN
                           | WITH
    <collection_keyword> ::= FOR_EACH
                           | FOR_ALL
    <precedence_keyword> ::= '('
                           | ')'