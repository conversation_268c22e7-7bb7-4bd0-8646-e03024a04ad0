﻿
# Flags

$global:AUTOUPLOAD = $true
$global:PROD_UPLOAD = $true
$global:COMBINE_FILES = $true
$global:DEBUG = $false

$msa = $false

if ($msa) {
    $global:AUTOUPLOAD = $false
    $global:PROD_UPLOAD = $false
    $global:COMBINE_FILES = $false
}


######################################################################################################################
# Version change log
# 1.1 [09/09/2021] Fixed Umbrellas mapping <PersDriver> to <BasicDriverInfo> and PersVeh and Watercraft as well
#                  Added feature of counting duplicate policies and reporting it on the output
#
# 1.2 [09/16/2021] Added AutoUpload so that resulting XML files no longer need to be uploaded manually
#                  Added feature of reporting the number of and list of uploaded files and saving to text file
#                  Added feature of reporting the number of Inland Marine policies and saving to text file
#                  NOTE: Both of these txt files are opened upon completion of the script
#
# 1.3 [10/15/2021] Added $global:AUTOUPLOAD flag to be able to turn on/off autouploads (set to $true for TSAs)
#                  Fixed Packaged Policy splitting by removing PersPolicy/Loss nodes from all splits except Home
#                  Fixed Packaged Policy splitting by removing nodes 'Watercraft' and 'WatercraftAccessory' from Auto policies
#                  Added the comment "[Safeco BT] This policy was derived from a Packaged Policy." to all split policies
#
# 1.4 [10/25/2021] Changed the conversion of FamilyNames so that it applies to all LOBs, not just packaged policies
#                  - this was discovered by Cher coming from Encore policies
#
# 1.5 [11/06/2021] Added merging of Inland Marine policies with matching Home policies
#                  - this includes ability to match policies using Mailing Address (seems to work well)
#                  - merging copied Coverage and all ScheduledProperty items to Home, updates Policy Premium and adds Remark
#                  Changed output to put everything into a single Log file, separating each agency file processed
# 2.0 [02/03/2022] Major changes so moving to new version
#                  Added feature of generating a single LOG text file that contains entire report and opens on completion
#                  Added feature of generating AgencyID by matching all Addr elements across all policies
#                  Added feature of generating a CustomerID report as .csv file based on AgencyID 
#                  Added feature of optional flag for CustomerID report (default is $true)
#                  Added feature of optional flag for Name report (default is $false)
#		           Fixed some defects discovered on merging Inland Marine with Home
#		           - had to take into account multiple Dwell nodes in Home, multiple Locations
#                  - had to generate new Locations and LocationRefs when necessary
#                  - Dwell nodes weren't being copied over when a new Location was found but now they are
#                  - Some INMRP policies don't contain a Dwell node so one is created when merging, if necessary
#                  - Added Fuzzy Matching routines to determine best matching Home policy when there's more than one to choose from
#                  - However, decision was made to NOT merge unless there is an EXACT match of Locations
# 2.1 [03/02/2022] Added removing TaxIds that are all zeros
#                  Added removing TaxIds that are invalid (<> 9 digits)
#
# 2.2 [03/14/2022] Added check for AdditionalInterest nodes found under PersPolicy; if so, move them to be under Location
#                  Eliminated the RemarkText to show Original name because of the downstream issue raised by Nancy Banks
#                  Changed names of stylesheet files
#
# 2.3 [03/17/2022] Added uploading of INMRP to AQE with the release to Production of support for INMRP
#
# 2.4 [04/15/2022] Modified AgencyID generation by adding in a timestamp for each invitation processed
#                  to avoid getting overlapping AgencyIDs when a book has multiple invitations
#
# 2.5 [04/22/2022] Added check for zip files with underscore characters at the beginning and end of filename
#                  If found, it renames them by removing the first and last underscore characters
#                  Note that it appears the underscores only occur on some files when downloaded individually so
#                  I do not check the unzipped zips for underscores. 
#
# 2.6 [03/01/2023] Replaced the upload URLs with the new ones provided by Quotabotz 
#
# 2.7 [03/10/2023] Fixed splitting out Umbrella to remove all Coverages under Dwell (should only retain Coverages under UmbrellaLineBusiness node 
#
# 2.8 [03/25/2025] Added change of MHOME (Mobile Home) LOBCd to HOME
#
######################################################################################################################


[System.GC]::Collect()
# The following are required for displaying pop-up windows for selecting folders
[void] [System.Reflection.Assembly]::LoadWithPartialName("System.Windows.Forms")
[System.Windows.Forms.Application]::EnableVisualStyles()
Add-Type -AssemblyName PresentationCore,PresentationFramework
Add-Type -AssemblyName  System.Windows.Forms 


$global:FLAG_Generate_Name_Report = $true
$global:FLAG_Generate_CustomerID_Report = $true

$global:ReportFile = $null
$global:NameCase = ''

# XPath Friendly Names
$global:InsuredString = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']"
$global:CoinsuredString = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']"
$global:LOBCdString = "//PersPolicy/LOBCd"
$global:PolicyNumberString = "//PersPolicy/PolicyNumber"
$global:AgencyId = "//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']/ItemIdInfo/AgencyId"
$global:MetaDataNode = 'com.Safeco_BookTransfer_Metadata'

$global:uploadList = New-Object System.Collections.ArrayList

$global:LOG = New-Object System.Collections.ArrayList
$global:STOPWATCH = $null

# For matching policies
$global:Key_CustomerID = $null
$global:CustomerID_PolicyData = $null
$global:AgencyIDTimeStamp = [DateTime]::Now.ToString("hhmmss") # this gets reset for every book
$global:MOBILEHOMEOWNERS = $false

$global:InlandMarinePolicies = $null
$global:HomePolicies = $null
$global:PersPolicyAdditionalInterests = 0
$global:InvalidTaxIds = 0
$global:Counts = @{ 
'PackagedPolicies' = 0 
'HomePolicies' = 0
'AutoPolicies' = 0
'BoatPolicies' = 0 
'DwellingPolicies' = 0
'InlandMarinePolicies' = 0
'PersPolicyAdditionalInterests' = 0
'InvalidTaxIds' = 0
'CustomerIDs' = 0 
}

$version = "2.8"
$author = "Dan Benson, Copyright (c) 2025, Safeco Book Transfer, All Rights Reserved"


######################################################################################################################
$global:STYLESHEET_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\_XML Stylesheets\Production Versions\Current Production"

$global:AllPrefixes = @('ADM','ATTY','CAPT','CHIEF','CMDR','COL','DR',`
'GOV','HON','MAJ','MR','MRS','MS','PRINCE','PROF','RABBI','REV','BROTHER','SISTER','FATHER','COUNT')

$global:AllSuffixes = @('II','III','IV','CPA','DDS','ESQ','JD','JR','LLD',`
'MD','PHD','RET','RN','SR','TRUSTEE')

$global:Ignores = @('LLC','IRREVOCABLE','REVOCABLE','TRUST','PROPERTIES','ESTATE','EST','DEVELOPMENT',`
'REALTY','GROUP','VENTURES','PROPERTY','MANAGEMENT','MNGMT','ASSETS','ENTERPRISES')

######################################################################################################################
# FUZZY MATCHING
######################################################################################################################
function Get-FuzzyMatchScore {
    [CmdletBinding()]
    param (
        [Parameter(Position = 0)]
        [string] $Search,

        [Parameter(Position = 1)]
        [string] $String
    )

    $score = 100

    # Use approximate string matching to get some values needed to calculate the score of the result
    $longestCommonSubstring = Get-LongestCommonSubstring –String1 $String –String2 $Search
    $levenshteinDistance = Get-LevenshteinDistance –String1 $String –String2 $Search
    $commonPrefix = Get-CommonPrefix –String1 $String –String2 $Search

    # By running the result through this regex pattern we get the length of the match as well as the
    # the index of where the match starts. The shorter the match length and the index, the more
    # score will be added for the match.
    $regexMatchFilter = $Search.ToCharArray() -join '.*?'
    $match = Select-String –InputObject $String –Pattern $regexMatchFilter –AllMatches
    $matchLength = ($match.Matches | Sort-Object Length | Select-Object –First 1).Value.Length
    $matchIndex = ($match.Matches | Sort-Object Length | Select-Object –First 1).Index

    # Calculate score
    $score = $score – $levenshteinDistance
    $score = $score * $longestCommonSubstring.Length
    $score = $score – $matchLength
    $score = $score – $matchIndex

    if ($commonPrefix) {
        $score =  $score + $commonPrefix.Length
    }

    Write-Output $score
}
######################################################################################################################
Function Get-HammingDistance {
    <#
        .SYNOPSIS
            Get the Hamming Distance between two strings or two positive integers.
        .DESCRIPTION
            The Hamming distance between two strings of equal length is the number of positions at which the
            corresponding symbols are different. In another way, it measures the minimum number of substitutions
            required to change one string into the other, or the minimum number of errors that could have
            transformed one string into the other. Note! Even though the original Hamming algorithm only works for
            strings of equal length, this function supports strings of unequal length as well.
            The function also calculates the Hamming distance between two positive integers (considered as binary
            values); that is, it calculates the number of bit substitutions required to change one integer into
            the other.
        .EXAMPLE
            Get-HammingDistance 'karolin' 'kathrin'
            Calculate the Hamming distance between the two strings. The result is 3.
        .EXAMPLE
            Get-HammingDistance 'karolin' 'kathrin' -NormalizedOutput
            Calculate the normalized Hamming distance between the two strings. The result is 0.571428571428571.
        .EXAMPLE
            Get-HammingDistance -Int1 61 -Int2 15
            Calculate the hamming distance between 61 and 15. The result is 3.
        .LINK
            http://en.wikipedia.org/wiki/Hamming_distance
            https://communary.wordpress.com/
            https://github.com/gravejester/Communary.PASM
        .NOTES
            Author: Ã˜yvind Kallstad
            Date: 03.11.2014
            Version: 1.0
    #>
    [CmdletBinding(DefaultParameterSetName = 'String')]
    param (
        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = 'String')]
        [ValidateNotNullOrEmpty()]
        [string] $String1,

        [Parameter(Position = 1, Mandatory = $true, ParameterSetName = 'String')]
        [ValidateNotNullOrEmpty()]
        [string] $String2,

        [Parameter(Position = 0, Mandatory = $true, ParameterSetName = 'Integer')]
        [ValidateNotNullOrEmpty()]
        [uint32] $Int1,

        [Parameter(Position = 1, Mandatory = $true, ParameterSetName = 'Integer')]
        [ValidateNotNullOrEmpty()]
        [uint32] $Int2,

        # Makes matches case-sensitive. By default, matches are not case-sensitive.
        [Parameter(ParameterSetName = 'String')]
        [switch] $CaseSensitive,

        # Normalize the output value. When the output is not normalized the maximum value is the length of the longest string, and the minimum value is 0,
        # meaning that a value of 0 is a 100% match. When the output is normalized you get a value between 0 and 1, where 1 indicates a 100% match.
        [Parameter(ParameterSetName = 'String')]
        [switch] $NormalizeOutput
    )

    try {
        if ($PSCmdlet.ParameterSetName -eq 'String') {
            # handle case insensitivity
            if (-not($CaseSensitive)) {
                $String1 = $String1.ToLowerInvariant()
                $String2 = $String2.ToLowerInvariant()
            }

            # set initial distance
            $distance = 0

            # get max and min length of the input strings
            $maxLength = [Math]::Max($String1.Length,$String2.Length)
            $minLength = [Math]::Min($String1.Length,$String2.Length)

            # calculate distance for the length of the shortest string
            for ($i = 0; $i -lt $minLength; $i++) {
                if (-not($String1[$i] -ceq $String2[$i])) {
                    $distance++
                }
            }

            # add the remaining length to the distance
            $distance = $distance + ($maxLength – $minLength)

            if ($NormalizeOutput) {
                Write-Output (1 – ($distance / $maxLength))
            }

            else {
                Write-Output $distance
            }
        }

        else {
            $distance = 0
            $value = $Int1 -bxor $Int2
            while ($value -ne 0) {
                $distance++
                $value = $value -band ($value – 1)
            }
            Write-Output $distance
        }
    }

    catch {
        Write-Warning $_.Exception.Message
    }
}
######################################################################################################################
Function Get-LevenshteinDistance {
    <#
        .SYNOPSIS
            Get the Levenshtein distance between two strings.
        .DESCRIPTION
            The Levenshtein Distance is a way of quantifying how dissimilar two strings (e.g., words) are to one another by counting the minimum number of operations required to transform one string into the other.
        .EXAMPLE
            Get-LevenshteinDistance 'kitten' 'sitting'
        .LINK
            http://en.wikibooks.org/wiki/Algorithm_Implementation/Strings/Levenshtein_distance#C.23
            http://en.wikipedia.org/wiki/Edit_distance
            https://communary.wordpress.com/
            https://github.com/gravejester/Communary.PASM
        .NOTES
            Author: Ã˜yvind Kallstad
            Date: 07.11.2014
            Version: 1.0
    #>
    [CmdletBinding()]
    param(
        [Parameter(Position = 0)]
        [string]$String1,

        [Parameter(Position = 1)]
        [string]$String2,

        # Makes matches case-sensitive. By default, matches are not case-sensitive.
        [Parameter()]
        [switch] $CaseSensitive,

        # A normalized output will fall in the range 0 (perfect match) to 1 (no match).
        [Parameter()]
        [switch] $NormalizeOutput
    )

    if (-not($CaseSensitive)) {
        $String1 = $String1.ToLowerInvariant()
        $String2 = $String2.ToLowerInvariant()
    }

    $d = New-Object 'Int[,]' ($String1.Length + 1), ($String2.Length + 1)

    try {
        for ($i = 0; $i -le $d.GetUpperBound(0); $i++) {
            $d[$i,0] = $i
        }

        for ($i = 0; $i -le $d.GetUpperBound(1); $i++) {
            $d[0,$i] = $i
        }

        for ($i = 1; $i -le $d.GetUpperBound(0); $i++) {
            for ($j = 1; $j -le $d.GetUpperBound(1); $j++) {
                $cost = [Convert]::ToInt32((-not($String1[$i–1] -ceq $String2[$j–1])))
                $min1 = $d[($i–1),$j] + 1
                $min2 = $d[$i,($j–1)] + 1
                $min3 = $d[($i–1),($j–1)] + $cost
                $d[$i,$j] = [Math]::Min([Math]::Min($min1,$min2),$min3)
            }
        }

        $distance = ($d[$d.GetUpperBound(0),$d.GetUpperBound(1)])

        if ($NormalizeOutput) {
            Write-Output (1 – ($distance) / ([Math]::Max($String1.Length,$String2.Length)))
        }

        else {
            Write-Output $distance
        }
    }

    catch {
        Write-Warning $_.Exception.Message
    }
}
######################################################################################################################
Function Get-LongestCommonSubstring {
    <#
        .SYNOPSIS
            Get the longest common substring of two strings.
        .DESCRIPTION
            Get the longest common substring of two strings.
        .EXAMPLE
            Get-LongestCommonSubstring 'Karolin' 'kathrin' -CaseSensitive
        .LINK
            https://fuzzystring.codeplex.com/
            http://en.wikipedia.org/wiki/Longest_common_substring_problem
            https://communary.wordpress.com/
            https://github.com/gravejester/Communary.PASM
        .NOTES
            Adapted to PowerShell from code by Kevin Jones (https://fuzzystring.codeplex.com/)
            Author: Ã˜yvind Kallstad
            Date: 03.11.2014
            Version: 1.0
    #>
    [CmdletBinding()]
    param (
        [Parameter(Position = 0)]
        [string] $String1,

        [Parameter(Position = 1)]
        [string] $String2,

        [Parameter()]
        [switch] $CaseSensitive
    )

    if (-not($CaseSensitive)) {
        $String1 = $String1.ToLowerInvariant()
        $String2 = $String2.ToLowerInvariant()
    }

    $array = New-Object 'Object[,]' $String1.Length, $String2.Length
    $stringBuilder = New-Object System.Text.StringBuilder
    $maxLength = 0
    $lastSubsBegin = 0

    for ($i = 0; $i -lt $String1.Length; $i++) {
        for ($j = 0; $j -lt $String2.Length; $j++) {
            if ($String1[$i] -cne $String2[$j]) {
                $array[$i,$j] = 0
            }
            else {
                if (($i -eq 0) -or ($j -eq 0)) {
                    $array[$i,$j] = 1
                }
                else {
                    $array[$i,$j] = 1 + $array[($i – 1),($j – 1)]
                }
                if ($array[$i,$j] -gt $maxLength) {
                    $maxLength = $array[$i,$j]
                    $thisSubsBegin = $i – $array[$i,$j] + 1
                    if($lastSubsBegin -eq $thisSubsBegin) {
                        [void]$stringBuilder.Append($String1[$i])
                    }
                    else {
                        $lastSubsBegin = $thisSubsBegin
                        $stringBuilder.Length = 0
                        [void]$stringBuilder.Append($String1.Substring($lastSubsBegin, (($i + 1) – $lastSubsBegin)))
                    }
                }
            }
        }
    }

    Write-Output $stringBuilder.ToString()
}
######################################################################################################################
Function Get-CommonPrefix {
    <#
        .SYNOPSIS
            Find the common prefix of two strings.
        .DESCRIPTION
            This function will get the common prefix of two strings; that is, all
            the letters that they share, starting from the beginning of the strings.
        .EXAMPLE
            Get-CommonPrefix 'Card' 'Cartoon'
            Will get the common prefix of both string. Should output 'car'.
        .LINK
            https://communary.wordpress.com/
            https://github.com/gravejester/Communary.PASM
        .INPUTS
            System.String
        .OUTPUTS
            System.String
        .NOTES
            Author: Ã˜yvind Kallstad
            Date: 03.11.2014
            Version 1.1
            Dependencies: none
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, Position = 0)]
        [ValidateNotNullOrEmpty()]
        [string]$String1,

        [Parameter(Mandatory = $true, Position = 1)]
        [ValidateNotNullOrEmpty()]
        [string]$String2,

        # Maximum length of the returned prefix.
        [Parameter()]
        [int]$MaxPrefixLength,

        # Makes matches case-sensitive. By default, matches are not case-sensitive.
        [Parameter()]
        [switch] $CaseSensitive
    )

    if (-not($CaseSensitive)) {
        $String1 = $String1.ToLowerInvariant()
        $String2 = $String2.ToLowerInvariant()
    }

    $outputString = New-Object 'System.Text.StringBuilder'
    $shortestStringLength = [Math]::Min($String1.Length,$String2.Length)

    # Let the maximum prefix length be the same as the length of the shortest of
    # the two input strings, unless defined by the MaxPrefixLength parameter.
    if (($shortestStringLength -lt $MaxPrefixLength) -or ($MaxPrefixLength -eq 0)) {
        $MaxPrefixLength = $shortestStringLength
    }

    # Loop from the start and add any characters found that are equal
    for ($i = 0; $i -lt $MaxPrefixLength; $i++) {
        if ($String1[$i] -ceq $String2[$i]) {
            [void]$outputString.Append($String1[$i])
        }
        else { break }
    }

    Write-Output $outputString.ToString()
}
######################################################################################################################
Function Fuzzy_Match () {
    Param(        
        [Parameter(Mandatory=$true)]
        $String1,   
        [Parameter(Mandatory=$true)]
        $String2
        )
    $one = Get-LongestCommonSubstring $String1 $String2
    $two = Get-FuzzyMatchScore $String1 $String2
    $three = Get-HammingDistance $String1 $String2
    $four = Get-LevenshteinDistance $String1 $String2
    $score = $one.Length + $two - $three - $four
    return $score
}
######################################################################################################################
######################################################################################################################
Function Add_Remark () {
    Param(
        [Parameter(Mandatory=$true)]
        $ACORD_XML,    
        [Parameter(Mandatory=$true)]
        $RemarkString
        )
    switch (LOBCd_From_ACORD_XML($ACORD_XML)) {
        "AUTOP" { $QuoteInqRq = '//PersAutoPolicyQuoteInqRq' }
        "HOME"  { $QuoteInqRq = '//HomePolicyQuoteInqRq' }
        "DFIRE" { $QuoteInqRq = '//DwellFirePolicyQuoteInqRq' }
        "BOAT"  { $QuoteInqRq = '//WatercraftPolicyQuoteInqRq' }
        "UMBRP" { $QuoteInqRq = '//PersUmbrellaPolicyQuoteInqRq' }
        "INMRP" { $QuoteInqRq = '//PersInlandMarinePolicyQuoteInqRq' }
        "PPKGE" { $QuoteInqRq = '//PersPkgPolicyQuoteInqRq' }
        default { $QuoteInqRq = '' }
        }
    if ($QuoteInqRq -ne '') {
        $newRemarkNode = ($ACORD_XML.SelectSingleNode($QuoteInqRq)).AppendChild($ACORD_XML.CreateElement('RemarkText')) 
        [void]$newRemarkNode.AppendChild($ACORD_XML.CreateTextNode($RemarkString))
        }
    return $ACORD_XML
}
##############################################################################################################
Function Contains_Prefix ($NameString) {
    $SplitParts = $NameString.Trim().Replace('.','').Replace(',','').Split()
    foreach ($part in $SplitParts) { if ($global:AllPrefixes.Contains($part)) { return $true } }
    return $false
}
######################################################################################################################
Function Contains_Suffix ($NameString) {
    $SplitParts = $NameString.Trim().Replace('.','').Replace(',','').Split()
    foreach ($part in $SplitParts) { if ($global:AllSuffixes.Contains($part)) { return $true } }
    return $false
}
######################################################################################################################
######################################################################################################################
Function GetNamePartsForOneOrTwoPeople ($OneOrTwoPersonNameString) {

    $OneOrTwoPersonNameString = $OneOrTwoPersonNameString.Replace('?','')

    $LeftName = $null; $RightName = $null
    $i = $OneOrTwoPersonNameString.IndexOf('&')
    if ($i -gt 0) {
    # We need to determine whether there is a shared last name, if so, add it to both sides


  <#
        $PrimaryInsuredSuffix = " "
        # Check for a Suffix, if so, set it and remove it from NameParts
        $OneOrTwoPersonNameString = $OneOrTwoPersonNameString.Trim().Replace('.','').Replace(',','')
        $SplitParts = $OneOrTwoPersonNameString.Split()
        foreach ($part in $SplitParts) { 
            if ($global:AllSuffixes.Contains($part)) {
                return @{ 'Person1' = $LeftName; 'Person2' = $RightName; }
              
                # if we have a Suffix then remove it and set $PrimaryInsuredSuffix
                $OneOrTwoPersonNameString = $OneOrTwoPersonNameString.Replace($part,"").Trim()
                $i = $OneOrTwoPersonNameString.IndexOf('&') # need to reset i
                $PrimaryInsuredSuffix = " $part"
                break
             
                }
            }
   #>
        $LeftName = GetNameParts($OneOrTwoPersonNameString.SubString(0,$i-1) + "$PrimaryInsuredSuffix")
        $RightName = GetNameParts($OneOrTwoPersonNameString.SubString($i+1,$OneOrTwoPersonNameString.Length - $i - 1))
        # check for a single name on the left, e.g. John & Mary Smith
        $LeftParts = $OneOrTwoPersonNameString.SubString(0,$i-1).Trim().Replace('.','').Replace(',','').Split()
        if ($LeftParts.Count -eq 1) {
           # grab the last name from RightName
           #$SplitParts = $RightName.Trim().Replace('.','').Replace(',','').Split()
           $CommonLastName = $RightName.LastName
           $LeftName = GetNameParts($LeftParts[0] + ' ' + $CommonLastName)
           }
        if ($LeftParts.Count -eq 2) {
           $singleInitial = $LeftParts[1].Length
           if ($singleInitial -eq 1) {
               $CommonLastName = $RightName.LastName
               $LeftName = GetNameParts($LeftParts[0] + ' ' + $LeftParts[1] + ' ' + $CommonLastName)
               }
           }
        } 
    else {
        $LeftName = GetNameParts($OneOrTwoPersonNameString)
        $RightName = ""
        }
    return @{ 'Person1' = $LeftName; 'Person2' = $RightName; } 
}
######################################################################################################################
######################################################################################################################
Function GetNameParts ($OnePersonNameString) {
    $NameParts = New-Object -TypeName System.Collections.ArrayList
    $prefix = ""; $suffix = "";
    $firstname = ""; $middlename = ""; $lastname = "";

    # Check for the pattern 'LAST, FIRST' and switch it around to 'FIRST LAST'
    $comma = $OnePersonNameString.IndexOf(',')
    if ($comma -gt 0) {
        $lastName = $OnePersonNameString.SubString(0, $comma)
        $theRest = $OnePersonNameString.SubString($comma+1,$OnePersonNameString.Length - $comma - 1)
        # Put last name on the end
        $OnePersonNameString = $OnePersonNameString.SubString($comma+1,$OnePersonNameString.Length - $comma - 1) + " " + $lastName
        }
 
    # Eliminate chars: . , 
    $SplitParts = $OnePersonNameString.Trim().Replace('.','').Replace(',','').Split()
    foreach ($part in $SplitParts) { [void] $NameParts.Add($part) }

    # Check for a Prefix, if so, set it and remove it from NameParts
    if ($global:AllPrefixes.Contains($NameParts[0].ToUpper())) { 
        $prefix = $NameParts[0].Trim()
        [void] $NameParts.RemoveAt(0)
        #write-host "HOORAY found Prefix $prefix"
        #foreach ($part in $NameParts) { write-host $part }
        }

    # Check for a Suffix, if so, set it and remove it from NameParts
    if ($global:AllSuffixes.Contains($NameParts[$NameParts.Count-1].ToUpper())) { 
        $suffix = $NameParts[$NameParts.Count-1].Trim()
        [void] $NameParts.RemoveAt($NameParts.Count-1)
        #write-host "HOORAY found Suffix $suffix"
        #foreach ($part in $NameParts) { write-host $part }
        }

   # Check for case of just first 
   if ($NameParts.Count -eq 1) { 
        $firstname = $NameParts[0].Trim()
        $NameParts = @() # empty it out, we're done
        }

   # Check for simplest case of just first and last
   if ($NameParts.Count -eq 2) { 
        $firstname = $NameParts[0].Trim()
        $lastname = $NameParts[1].Trim()
        $NameParts = @() # empty it out, we're done
        }

   # Check for next simplest case of first middle last
   if ($NameParts.Count -eq 3) { 
        $firstname = $NameParts[0].Trim()
        $middlename = $NameParts[1].Trim()
        $lastname = $NameParts[2].Trim()
        $NameParts = @() # empty it out, we're done
        }

   # Check for more complicated names with more than 3 parts
   if ($NameParts.Count -ge 3) {
        # See if any part is an initial, if so, use that as the middle name, then first is all on left, last is all on right
        for ($k=1; $k -lt ($NameParts.Count-1); $k++) { # skip the first and last ones
            if ($NameParts[$k].Length -eq 1) { 
                $middlename = $NameParts[$k]
                for ($j=0; $j -lt $k; $j++) { $firstname = $firstname + $NameParts[$j] + ' ' }
                $firstname = $firstname.Trim()
                for ($j=$k+1; $j -lt $NameParts.Count; $j++) { $lastname = $lastname + $NameParts[$j] + ' ' }
                $lastname = $lastname.Trim()
                $NameParts = @() # empty it out, we're done
                }
            }
        }

    return @{ 'Prefix' = $prefix; 'FirstName' = $firstname; 'MiddleName' = $middlename; 'LastName' = $lastname; 'Suffix' = $suffix } 
}
######################################################################################################################
Function CheckForPrefix ($ACORD_XML) {
    # Check to see whether a Prefix is in the GivenName field and not in the TitlePrefix field
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $GivenName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName
    if ($GivenName) {
        $ExistingPrefix = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix
        if ($ExistingPrefix -ne $null) { return $ACORD_XML }
        $GivenName = $GivenName.Trim().Replace('.','').Replace(',','').Replace('(',' ').Replace(')',' ')
        $SplitParts = $GivenName.Split()
        foreach ($part in $SplitParts) { if ($global:AllPrefixes.Contains($part.ToUpper())) {
            #write-host "We found a prefix in $GivenName -- $part !"
                # we know TitlePrefix is null so create a new one and remove it from GivenName
                $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName = $GivenName.Replace($part,"").Trim()
                $newPrefix = ($PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName).AppendChild($ACORD_XML.CreateElement('TitlePrefix'));
                [void] $newPrefix.AppendChild($ACORD_XML.CreateTextNode($part));
                break;
                } 
            }
        }
return $ACORD_XML
}
######################################################################################################################
Function CheckForSuffix ($ACORD_XML) {
    # Check to see whether a Suffix is in the Surname field and not in the NameSuffix field
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $SurName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.SurName
    if ($SurName) {
        $ExistingSuffix = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix
        if ($ExistingSuffix -ne $null) { return $ACORD_XML }
        $SurName = $SurName.Trim().Replace('.','').Replace(',','').Replace('(',' ').Replace(')',' ')
        $SplitParts = $SurName.Split()
        foreach ($part in $SplitParts) { if ($global:AllSuffixes.Contains($part.ToUpper())) {
            #write-host "We found a suffix in $SurName -- $part !"
                # we know TitlePrefix is null so create a new one and remove it from GivenName
                $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.SurName = $SurName.Replace($part,"").Trim()
                $newSuffix = ($PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName).AppendChild($ACORD_XML.CreateElement('NameSuffix'));
                [void] $newSuffix.AppendChild($ACORD_XML.CreateTextNode($part));
                break;
                } 
            }
        }
return $ACORD_XML
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Clean_IOP_Name_Fields () {
    Param(        
        [Parameter(Mandatory=$true)]
        $ACORD_XML
        )

    $IOP = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $IOPPersonName = $IOP.GeneralPartyInfo.NameInfo.PersonName
    if ($IOPPersonName -ne $null) {
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix = ($IOP.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.GivenName = ($IOP.GeneralPartyInfo.NameInfo.PersonName.GivenName).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName = ($IOP.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.Surname = ($IOP.GeneralPartyInfo.NameInfo.PersonName.Surname).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.NameSuffix = ($IOP.GeneralPartyInfo.NameInfo.PersonName.NameSuffix).Replace('?','') } catch { }
        }
    $IOP = $ACORD_XML.SelectSingleNode($global:CoinsuredString)
    $IOPPersonName = $IOP.GeneralPartyInfo.NameInfo.PersonName
    if ($IOPPersonName -ne $null) {
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix = ($IOP.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.GivenName = ($IOP.GeneralPartyInfo.NameInfo.PersonName.GivenName).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName = ($IOP.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.Surname = ($IOP.GeneralPartyInfo.NameInfo.PersonName.Surname).Replace('?','') } catch { }
        try { $IOP.GeneralPartyInfo.NameInfo.PersonName.NameSuffix = ($IOP.GeneralPartyInfo.NameInfo.PersonName.NameSuffix).Replace('?','') } catch { }
        }
    return $ACORD_XML
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Set_Insured_PersonName () {
    Param(        
        [Parameter(Mandatory=$true)]
        $ACORD_XML,    
        [Parameter(Mandatory=$true)]
        $PersonName
        )

$name = $PersonName.Prefix + ' ' + $PersonName.FirstName + ' ' + $PersonName.MiddleName + ' ' + $PersonName.LastName + ' ' + $PersonName.Suffix
#write-host "Setting $name as Insured"

    $IOP = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $IOPPersonName = $IOP.GeneralPartyInfo.NameInfo.PersonName
    if ($IOPPersonName -eq $null) { $IOPPersonName = ($IOP.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement('PersonName')) } 

    # Update Primary Insured with Primary name parts
    if ($PersonName.Prefix) {
        $TitlePrefix = $IOPPersonName.TitlePrefix
        if ($TitlePrefix -eq $null) { 
            $TitlePrefix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('TitlePrefix'))
            [void] $TitlePrefix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Prefix)) 
            }
        else { $IOPPersonName.TitlePrefix = $PersonName.Prefix }
        }
    if ($PersonName.FirstName) {
        $GivenName = $IOPPersonName.GivenName
        if ($GivenName -eq $null) { 
            $GivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('GivenName'))
            [void] $GivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.FirstName)) 
            }
        else { $IOPPersonName.GivenName = $PersonName.FirstName }
        }
    if ($PersonName.MiddleName) {
        $OtherGivenName = $IOPPersonName.OtherGivenName
        if ($OtherGivenName -eq $null) { 
            $OtherGivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('OtherGivenName'))
            [void] $OtherGivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.MiddleName)) 
            }
        else { $IOPPersonName.OtherGivenName = $PersonName.MiddleName }
        }
    if ($PersonName.LastName) {
        $Surname = $IOPPersonName.Surname
        if ($Surname -eq $null) { 
            $Surname = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('Surname'))
            [void] $Surname.AppendChild($ACORD_XML.CreateTextNode($PersonName.LastName)) 
            }
        else { $IOPPersonName.Surname = $PersonName.LastName }
        }
    if ($PersonName.Suffix) {
        $NameSuffix = $IOPPersonName.NameSuffix
        if ($NameSuffix -eq $null) { 
            $NameSuffix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('NameSuffix'))
            [void] $NameSuffix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Suffix)) 
            }
        else { $IOPPersonName.NameSuffix = $PersonName.Suffix }
        }
    return $ACORD_XML
}
######################################################################################################################
######################################################################################################################
Function Set_Coinsured_PersonName () {
    Param(        
        [Parameter(Mandatory=$true)]
        $ACORD_XML,    
        [Parameter(Mandatory=$true)]
        $PersonName
        )

$PN = $PN = ($ACORD_XML.SelectSingleNode("//PolicyNumber")).InnerXML
if ($PN -eq 'CASD215174') {
    #write-host "Here"
    }

$name = $PersonName.Prefix + ' ' + $PersonName.FirstName + ' ' + $PersonName.MiddleName + ' ' + $PersonName.LastName + ' ' + $PersonName.Suffix
#write-host "Setting $name as Coinsured"

    $IOP = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $CoInsured = $ACORD_XML.SelectSingleNode($global:CoinsuredString)
    if ($CoInsured -eq $null) {
        $CoInsured = $ACORD_XML.CreateElement('InsuredOrPrincipal')
        
        $InsuredOrPrincipalInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalInfo'))
        $InsuredOrPrincipalRoleCd = $InsuredOrPrincipalInfo.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalRoleCd'));
        [void] $InsuredOrPrincipalRoleCd.AppendChild($ACORD_XML.CreateTextNode('Coinsured'))

        $GeneralPartyInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('GeneralPartyInfo'))
        $NameInfo = $GeneralPartyInfo.AppendChild($ACORD_XML.CreateElement('NameInfo'))
        $IOPPersonName = $NameInfo.AppendChild($ACORD_XML.CreateElement('PersonName'))
        $TitlePrefix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('TitlePrefix'))
        [void] $TitlePrefix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Prefix))
        $GivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('GivenName'))
        [void] $GivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.FirstName))
        $OtherGivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('OtherGivenName'))
        [void] $OtherGivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.MiddleName))
        $Surname = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('Surname'))
        [void] $Surname.AppendChild($ACORD_XML.CreateTextNode($PersonName.LastName))
        $NameSuffix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('NameSuffix'))
        [void] $NameSuffix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Suffix))

        [void] ($IOP.ParentNode).InsertAfter($CoInsured,$IOP)
        }
    else {

        $IOPPersonName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName
        if ($IOPPersonName -eq $null) { $IOPPersonName = ($CoInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement('PersonName')) } 
   
        # Update Primary Insured with Primary name parts
        if ($PersonName.Prefix) {
            $TitlePrefix = $IOPPersonName.TitlePrefix
            if ($TitlePrefix -eq $null) { 
                $TitlePrefix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('TitlePrefix'))
                [void] $TitlePrefix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Prefix)) 
                }
            else { $IOPPersonName.TitlePrefix = $PersonName.Prefix }
            }
        if ($PersonName.FirstName) {
            $GivenName = $IOPPersonName.GivenName
            if ($CoInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName -eq $null) { 
                $GivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('GivenName'))
                [void] $GivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.FirstName)) 
                }
            else { $IOPPersonName.GivenName = $PersonName.FirstName }
            }
        if ($PersonName.MiddleName) {
            $OtherGivenName = $IOPPersonName.OtherGivenName
            if ($OtherGivenName -eq $null) { 
                $OtherGivenName = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('OtherGivenName'))
                [void] $OtherGivenName.AppendChild($ACORD_XML.CreateTextNode($PersonName.MiddleName)) 
                }
            else { $IOPPersonName.OtherGivenName = $PersonName.MiddleName }
            }
        if ($PersonName.LastName) {
            $Surname = $IOPPersonName.Surname
            if ($Surname -eq $null) { 
                $Surname = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('Surname'))
                [void] $Surname.AppendChild($ACORD_XML.CreateTextNode($PersonName.LastName)) 
                }
            else { $IOPPersonName.Surname = $PersonName.LastName }
            }
        if ($PersonName.Suffix) {
            $NameSuffix = $IOPPersonName.NameSuffix
            if ($NameSuffix -eq $null) { 
                $NameSuffix = $IOPPersonName.AppendChild($ACORD_XML.CreateElement('NameSuffix'))
                [void] $NameSuffix.AppendChild($ACORD_XML.CreateTextNode($PersonName.Suffix)) 
                }
            else { $IOPPersonName.NameSuffix = $PersonName.Suffix }
            }
        }
    return $ACORD_XML
}
######################################################################################################################
######################################################################################################################
Function Check_And_Adjust_Names ($ACORD_XML) {
    $ACORD_XML = Clean_IOP_Name_Fields -ACORD_XML $ACORD_XML

<#
    # Check whether Surname has an ampersand, if so, leave it alone!
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $SurName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.SurName
    if (($SurName) -and (($SurName.IndexOf('&') -gt 0) -or ($SurName.ToUpper().IndexOf(' AND ') -gt 0))) { write-host "Uh Oh! SurName has an & skipping! --$SurName"; return $ACORD_XML }

    # Check to see whether a Prefix is in the GivenName field and not in the TitlePrefix field
#    $ACORD_XML = CheckForPrefix $ACORD_XML
#    $ACORD_XML = CheckForSuffix $ACORD_XML
#>
    $PN = ($ACORD_XML.SelectSingleNode("//PolicyNumber")).InnerXML
    #write-host $PN
$PoliciesToWatch = @( `
'GH7925258520', `
'GU7925258520', `
'GA7925258520'
)


    if ($PoliciesToWatch.Contains($PN)){
         write-host "here"
        }

    $PrimaryInsured = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $CoInsured = $ACORD_XML.SelectSingleNode($global:CoinsuredString)
<#
# Check for no CommercialName but has Insured and no Coinsured
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    $InsuredPersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
    $CoinsuredPersonName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName
    if ($InsuredPersonName -and ($CommercialName -eq $null) -and ($CoinsuredPersonName -eq $null)) {
        $FullInsuredName = ($InsuredPersonName.TitlePrefix + ' ' + $InsuredPersonName.GivenName + ' ' + $InsuredPersonName.OtherGivenName + ' ' + $InsuredPersonName.SurName + ' ' + $InsuredPersonName.NameSuffix + ' ').Trim()
        $CommlNameNode = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName
        if ($CommlNameNode -eq $null) { $CommlNameNode = ($PrimaryInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement("CommlName")) }
        $CommercialNameNode = $CommlNameNode.CommercialName
        if ($CommercialNameNode -eq $null) { $CommercialNameNode = $CommlNameNode.AppendChild($ACORD_XML.CreateElement("CommercialName")) }
        [void] $CommercialNameNode.AppendChild($ACORD_XML.CreateTextNode($NewCommercialName))
        }
#>

# Easy #0
# Check for no CommercialName but has Insured and CoInsured
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    $InsuredPersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
    $CoinsuredPersonName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName
    if ($InsuredPersonName -and $CoinsuredPersonName -and ($CommercialName -eq $null)) {
        $FullInsuredName = ($InsuredPersonName.TitlePrefix + ' ' + $InsuredPersonName.GivenName + ' ' + $InsuredPersonName.OtherGivenName + ' ' + $InsuredPersonName.SurName + ' ' + $InsuredPersonName.NameSuffix + ' ').Trim()
        $names = GetNamePartsForOneOrTwoPeople($FullInsuredName)
        $Primary = $names.Person1
        $ACORD_XML = Set_Insured_PersonName -ACORD_XML $ACORD_XML -PersonName $Primary
        $FullCoinsuredName = ($CoinsuredPersonName.TitlePrefix + ' ' + $CoinsuredPersonName.GivenName + ' ' + $CoinsuredPersonName.OtherGivenName + ' ' + $CoinsuredPersonName.SurName + ' ' + $CoinsuredPersonName.NameSuffix + ' ').Trim()
        $names = GetNamePartsForOneOrTwoPeople($FullCoinsuredName)
        $Primary = $names.Person1
        $ACORD_XML = Set_Coinsured_PersonName -ACORD_XML $ACORD_XML -PersonName $Primary 
        $InsuredPersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
        $CoinsuredPersonName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName
        $NewInsuredName = ($InsuredPersonName.TitlePrefix + ' ' + $InsuredPersonName.GivenName + ' ' + $InsuredPersonName.OtherGivenName + ' ' + $InsuredPersonName.SurName + ' ' + $InsuredPersonName.NameSuffix + ' ').Trim()
        $NewCoinsuredName = ($CoinsuredPersonName.TitlePrefix + ' ' + $CoinsuredPersonName.GivenName + ' ' + $CoinsuredPersonName.OtherGivenName + ' ' + $CoinsuredPersonName.SurName + ' ' + $CoinsuredPersonName.NameSuffix + ' ').Trim()
        $NewCommercialName = $NewInsuredName + ' & ' + $NewCoinsuredName 

        $CommlNameNode = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName
        if ($CommlNameNode -eq $null) { $CommlNameNode = ($PrimaryInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement("CommlName")) }
        $CommercialNameNode = $CommlNameNode.CommercialName
        if ($CommercialNameNode -eq $null) { $CommercialNameNode = $CommlNameNode.AppendChild($ACORD_XML.CreateElement("CommercialName")) }
        [void] $CommercialNameNode.AppendChild($ACORD_XML.CreateTextNode($NewCommercialName))

        $global:NameCase = 'Easy 0'
        #write-host "$PN : $global:NameCase"
        Add_Metadata -XMLDocument $ACORD_XML -NodeName 'Original_Name' -Value "$FullInsuredName & $FullCoinsuredName"
        return $ACORD_XML
        }


# Easy #1
# Check first for no CommercialName and no Coinsured and no '&' in PrimaryInsured name
    $PrimaryInsured = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $CoInsured = $ACORD_XML.SelectSingleNode($global:CoinsuredString)
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    $PersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
    $NewCommercialName = ($PersonName.TitlePrefix + ' ' + $PersonName.GivenName + ' ' + $PersonName.OtherGivenName + ' ' + $PersonName.SurName + ' ' + $PersonName.NameSuffix + ' ').Trim()
    $NewCommercialName = $NewCommercialName.Replace('  ',' ').Replace(' and ',' & ').Replace(' AND ',' & ').Trim()
    $ampersand = $NewCommercialName.IndexOf('&')
    if (($CoInsured -eq $null) -and ($CommercialName -eq $null) -and ($ampersand -eq -1)) {
        # All we have to do is clean up Primary Insured name and set the new CommercialName and we're done
        $names = GetNamePartsForOneOrTwoPeople($NewCommercialName)
        $Primary = $names.Person1
        $ACORD_XML = Set_Insured_PersonName -ACORD_XML $ACORD_XML -PersonName $Primary
        $CommlNameNode = ($PrimaryInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement("CommlName"))
        $CommercialNameNode = $CommlNameNode.AppendChild($ACORD_XML.CreateElement("CommercialName"))
        [void] $CommercialNameNode.AppendChild($ACORD_XML.CreateTextNode($NewCommercialName))
        $global:NameCase = 'Easy 1'
        #write-host "$PN : $global:NameCase"
        Add_Metadata -XMLDocument $ACORD_XML -NodeName 'Original_Name' -Value $NewCommercialName
        return $ACORD_XML
        }

# Easy #2
# Check for CommercialName but no names in Insured or Coinsured
    if ($CommercialName) {
        $CleanedCommercialName = $CommercialName.Replace('  ',' ').Replace(' and ',' & ').Replace(' AND ',' & ').Trim()
        $CleanedCommercialName = Two_Names_With_Same_Last_Name($CleanedCommercialName)
        $ampersand = $CleanedCommercialName.IndexOf('&')
        if (($CoInsured -eq $null) -and ($CleanedCommercialName -ne $null) -and ($NewCommercialName -eq '') -and ($ampersand -eq -1)) {
            # All we have to do is set Insured name elements according to CommercialName and we're done
            $names = GetNamePartsForOneOrTwoPeople($CleanedCommercialName)
            $Primary = $names.Person1
            $ACORD_XML = Set_Insured_PersonName -ACORD_XML $ACORD_XML -PersonName $Primary
            $global:NameCase = 'Easy 2'
            #write-host "$PN : $global:NameCase"
            Add_Metadata -XMLDocument $ACORD_XML -NodeName 'Original_Name' -Value $CommercialName
            return $ACORD_XML
            }
        }

    $NewCommercialName = $null
    $PrimaryInsured = $ACORD_XML.SelectSingleNode($global:InsuredString)

    try { $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName } catch { $CommercialName = $null }
    if ($CommercialName -ne $null ) { $ACORD_XML = Distribute_CommercialName $ACORD_XML; return $ACORD_XML; }

    return $ACORD_XML
}
######################################################################################################################
Function Two_Names_With_Same_Last_Name ($CommercialName) {
# checks $CommercialName for two names with the same last name but no 'and' or '&'
# if so, it adds an &, otherwise no change
    if ($CommercialName -and ($CommercialName.IndexOf(' & ') -lt 0) -and ($CommercialName.ToUpper().IndexOf(' AND ') -lt 0)) {
        # commented to remove remval of commas $SplitParts = $CommercialName.Trim().Replace('.','').Replace(',','').Split()
        $SplitParts = $CommercialName.Replace('.','').Trim().Split()
        $lastPart = $SplitParts[$SplitParts.Count-1]
        for ($i = 0; $i -lt $SplitParts.Count-1; $i++) { 
            $NewCommercialName = $NewCommercialName + ' ' + $SplitParts[$i]
            if ($SplitParts[$i] -eq $lastPart) { $NewCommercialName = $NewCommercialName + ' &' } 
            }
        $NewCommercialName = $NewCommercialName + ' ' + $lastPart
        #write-host "$CommercialName is now $NewCommercialName"
        } else { $NewCommercialName = $CommercialName }
    return $NewCommercialName
}
######################################################################################################################
Function Distribute_CommercialName ($ACORD_XML) {
    $PN = ($ACORD_XML.SelectSingleNode("//PolicyNumber")).InnerXML

    $PrimaryInsured = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $CoInsured = $ACORD_XML.SelectSingleNode($global:CoinsuredString)

    $InsuredNameInfo = $PrimaryInsured.GeneralPartyInfo.NameInfo
 
    # Check for unusual CommercialNames
    $OriginalCommercialName = $InsuredNameInfo.CommlName.CommercialName
    if ($OriginalCommercialName) {
        $CleanedCommercialName = $OriginalCommercialName.Replace('  ',' ').Replace(' and ',' & ').Replace(' AND ',' & ').Trim()
        # Check if it contains two names with same last name but no 'and' or '&', if so, add & and keep going
        $CleanedCommercialName = Two_Names_With_Same_Last_Name($OriginalCommercialName)
        # Check for business and trust names
        $cname = $CleanedCommercialName.Trim()
        # Eliminate chars: . , ( ) 
        $SplitParts = $cname.Trim().Replace('.','').Replace('(',' ').Replace(')',' ').ToUpper().Split()
        #$SplitParts = $cname.Trim().Replace('.','').Replace(',','').Replace('(',' ').Replace(')',' ').ToUpper().Split()
        foreach ($part in $SplitParts) { 
            if ($global:Ignores.Contains($part)) { 
                $global:NameCase = 'Skip 1'
                #write-host "$PN : $global:NameCase"
                #write-host "skipping commercial name $cname"; return $ACORD_XML; 
                } 
            }

        # Check for a reversed name, e.g. <CommercialName>Kraszewski, Lynn</CommercialName>
        $comma = $cname.IndexOf(',')
        if ($comma -gt 0) {
            # Let's reverse the name
            $LeftHalf = $cname.Substring(0,$comma).Trim()
            $RightHalf = $cname.Substring($comma+1,$cname.Length-$comma-1).Trim()
            $CleanedCommercialName = $RightHalf + ' ' + $LeftHalf
            $InsuredNameInfo.CommlName.CommercialName = $CleanedCommercialName
            }

        # Check whether only the CommercialName Field exists. If so, fill in the PersonName parts, possibly create a new CoInsured
        if ($InsuredNameInfo.PersonName.GivenName -eq $null) {
            $InsuredNameInfo.CommlName.CommercialName = $CleanedCommercialName.Trim()
            # Check to see if & is the last character, if so, remove it
            if ($CleanedCommercialName[$CleanedCommercialName.Length-1] -eq '&') {
                $CleanedCommercialName = $CleanedCommercialName.SubString(0,$CleanedCommercialName.Length-1).Trim()
                $InsuredNameInfo.CommlName.CommercialName = $CleanedCommercialName
                }
            # No GivenName so we will create name parts from the CommercialName
            $names = GetNamePartsForOneOrTwoPeople($CleanedCommercialName)
            $Primary = $names.Person1
            $Secondary = $names.Person2
            $ACORD_XML = Set_Insured_PersonName -ACORD_XML $ACORD_XML -PersonName $Primary
            if ($Secondary -ne $null) { $ACORD_XML = Set_Coinsured_PersonName -ACORD_XML $ACORD_XML -PersonName $Secondary }
            $global:NameCase = 'Easy 3'
            #write-host "$PN : $global:NameCase"
            Add_Metadata -XMLDocument $ACORD_XML -NodeName 'Original_Name' -Value $OriginalCommercialName
            return $ACORD_XML
            }
        }

<#
        $PersonName = $InsuredNameInfo.PersonName
        if ($PersonName.PersonName -eq $null) { $PersonName = $InsuredNameInfo.AppendChild($ACORD_XML.CreateElement('PersonName')) }
        
        if ($Primary.Prefix -ne '') {
            $TitlePrefix = $PersonName.TitlePrefix
            if ($TitlePrefix -eq $null) { $TitlePrefix = $PersonName.AppendChild($ACORD_XML.CreateElement('TitlePrefix')) }
            [void] $TitlePrefix.AppendChild($ACORD_XML.CreateTextNode($Primary.Prefix));
            }
        if ($Primary.FirstName -ne '') {
            $GivenName = $PersonName.GivenName
            if ($GivenName -eq $null) { $GivenName = $PersonName.AppendChild($ACORD_XML.CreateElement('GivenName')) }
            [void] $GivenName.AppendChild($ACORD_XML.CreateTextNode($Primary.FirstName));
            }
        if ($Primary.MiddleName -ne '') {
            $OtherGivenName = $PersonName.OtherGivenName
            if ($OtherGivenName -eq $null) { $OtherGivenName = $PersonName.AppendChild($ACORD_XML.CreateElement('OtherGivenName')) }
            [void] $OtherGivenName.AppendChild($ACORD_XML.CreateTextNode($Primary.MiddleName));
            }
        if ($Primary.LastName -ne '') {
            $Surname = $PersonName.SurName
            if ($Surname -eq $null) { $Surname = $PersonName.AppendChild($ACORD_XML.CreateElement('Surname')) }
            [void] $Surname.AppendChild($ACORD_XML.CreateTextNode($Primary.LastName));
            }
        if ($Primary.Suffix -ne '') {
            $NameSuffix = $PersonName.NameSuffix
            if ($NameSuffix -eq $null) { $NameSuffix = $PersonName.AppendChild($ACORD_XML.CreateElement('NameSuffix')) }
            [void] $NameSuffix.AppendChild($ACORD_XML.CreateTextNode($Primary.Suffix));
            }

        # Now let's see if we need to create a CoInsured
        if (($Secondary -ne "") -and ($CoInsured -eq $null)) {
            $CoInsured = $PrimaryInsured.ParentNode.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipal'));
            $InsuredOrPrincipalInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalInfo'));
            $InsuredOrPrincipalRoleCd = $InsuredOrPrincipalInfo.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalRoleCd'));
            [void] $InsuredOrPrincipalRoleCd.AppendChild($ACORD_XML.CreateTextNode('Coinsured'));

            $GeneralPartyInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('GeneralPartyInfo'));
            $NameInfo = $GeneralPartyInfo.AppendChild($ACORD_XML.CreateElement('NameInfo'));
            $PersonName = $NameInfo.AppendChild($ACORD_XML.CreateElement('PersonName'))

            if ($Secondary.Prefix -ne '') {
            $TitlePrefix = $PersonName.TitlePrefix
            if ($TitlePrefix -eq $null) { $TitlePrefix = $PersonName.AppendChild($ACORD_XML.CreateElement('TitlePrefix')) }
            [void] $TitlePrefix.AppendChild($ACORD_XML.CreateTextNode($Secondary.Prefix));
            }
            if ($Secondary.FirstName -ne '') {
                $GivenName = $PersonName.GivenName
                if ($GivenName -eq $null) { $GivenName = $PersonName.AppendChild($ACORD_XML.CreateElement('GivenName')) }
                [void] $GivenName.AppendChild($ACORD_XML.CreateTextNode($Secondary.FirstName));
                }
            if ($Secondary.MiddleName -ne '') {
                $OtherGivenName = $PersonName.OtherGivenName
                if ($OtherGivenName -eq $null) { $OtherGivenName = $PersonName.AppendChild($ACORD_XML.CreateElement('OtherGivenName')) }
                [void] $OtherGivenName.AppendChild($ACORD_XML.CreateTextNode($Secondary.MiddleName));
                }
            if ($Secondary.LastName -ne '') {
                $Surname = $PersonName.SurName
                if ($Surname -eq $null) { $Surname = $PersonName.AppendChild($ACORD_XML.CreateElement('Surname')) }
                [void] $Surname.AppendChild($ACORD_XML.CreateTextNode($Secondary.LastName));
                }
            if ($Secondary.Suffix -ne '') {
                $NameSuffix = $PersonName.NameSuffix
                if ($NameSuffix -eq $null) { $NameSuffix = $PersonName.AppendChild($ACORD_XML.CreateElement('NameSuffix')) }
                [void] $NameSuffix.AppendChild($ACORD_XML.CreateTextNode($Secondary.Suffix));
                }
            }
    }
    #>

    return $ACORD_XML
}
######################################################################################################################
######################################################################################################################

<#
$NameReport = Join-Path -Path "C:\Users\<USER>\Downloads\Agencies\IVANS Bookroll\Name Processing" -ChildPath ("Name Report," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".csv")
Add-Content -Path $NameReport  -Value '"","Insured","","","","","CoInsured","","","",""'
Add-Content -Path $NameReport  -Value '"Input","Prefix","First","Middle","Last","Suffix","Prefix","First","Middle","Last","Suffix"'

foreach ($nm in $TestNames) {
    $names = GetNamePartsForOneOrTwoPeople($nm)

    $outstring = $nm + "," + $names.Person1.Prefix + "," + $names.Person1.FirstName + "," + `
                 $names.Person1.MiddleName + "," + $names.Person1.LastName + "," + $names.Person1.Suffix + "," + `
                 $names.Person2.Prefix + "," + $names.Person2.FirstName + "," + `
                 $names.Person2.MiddleName + "," + $names.Person2.LastName + "," + $names.Person2.Suffix + ","
    Add-Content -Path $NameReport  -Value $outstring
    }
invoke-item $NameReport
#>


######################################################################################################################
Function Process_IVANS_Bookroll_XML () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XMLFolder,   
        [Parameter(Mandatory=$true)]
        $DestinationFolder,   
        [Parameter(Mandatory=$true)]
        $BT_Record
        )

    if (!(Test-Path -Path $DestinationFolder)) { [void] (New-Item -Path $DestinationFolder -ItemType "Directory") }

<#    $HeaderSummary = Load_HeaderSummary_File $XMLFolder
    $MetaData = $HeaderSummary.BookrollRequestPoliciesSummary
    if ($MetaData -eq $null) { $MetaData = $HeaderSummary.ExpandedBookrollRequestPoliciesSummary }
    $AgencyName = $MetaData.AgencyName
    $AgencyAccount = $MetaData.AgencyAccount
    $TransferCode = $MetaData.TransferCode
    if ($TransferCode -ne $null) { $BT_Record = $TransferCode.Replace("BT","").Replace("-","").Trim() }
#>
    if ($global:FLAG_Generate_Name_Report) {
        $global:ReportFile = Join-Path -Path $DestinationFolder -ChildPath ("Name Report," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".csv")
        Add-Content -Path $global:ReportFile -Value '"Phase","LOB","Policy Number","Case","I Comm","I Pre","I GN","I OGN","I SN","I Suf","C Comm","C Pre","C GN","C OGN","C SN","C Suf"'
        }
    $XMLFiles = Get-ChildItem -Path $XMLFolder -Filter *.xml -File  | Sort name
    $totalFiles = $XMLFiles.count
    $duplicates = 0
    $i = 0
    Foreach ($file in $XMLFiles) {
        if ($file.BaseName -eq "HeaderSummary") { continue } # skip this one
 
        $theFile = $file.FullName        
        $RawXMLText = Get-content $theFile -raw

        # START Plain Text manipulation
        # For MHOME (Mobile Homeowners) policies, change to HOME:
        $global:MOBILEHOMEOWNERS = $false
        if (($RawXMLText.IndexOf('<LOBCd>MHOME</LOBCd>') -gt 0)) {
            $RawXMLText = $RawXMLText.Replace('<LOBCd>MHOME</LOBCd>','<LOBCd>HOME</LOBCd>')
            $global:MOBILEHOMEOWNERS = $true
        }

        # For Auto policies:
        $RawXMLText = $RawXMLText.Replace('<License>','<DriversLicense>').Replace('</License>','</DriversLicense>')
        $RawXMLText = $RawXMLText.Replace('<LicensePermitNumber>','<DriversLicenseNumber>').Replace('</LicensePermitNumber>','</DriversLicenseNumber>')
        
        # For all policies:
        $RawXMLText = $RawXMLText.Replace('PhysicalRisk','StreetAddress')
        $RawXMLText = $RawXMLText.Replace('<Amt>0</Amt>','<Amt></Amt>')
        $RawXMLText = $RawXMLText.Replace('<BirthDt>1900-01-01</BirthDt>','')
        $RawXMLText = $RawXMLText.Replace('<PhoneNumber>******-9999999</PhoneNumber>','<PhoneNumber></PhoneNumber>')
        $RawXMLText = $RawXMLText.Replace('<TaxId>*********</TaxId>','<TaxId></TaxId>')

        # For Umbrella policies only:
        if (($RawXMLText.IndexOf('PersUmbrellaPolicyQuoteInqRq') -gt 0)) {
            $RawXMLText = $RawXMLText.Replace("<PersVeh ","<BasicVehInfo ").Replace("</PersVeh>","</BasicVehInfo>")
            $RawXMLText = $RawXMLText.Replace("<PersDriver ","<BasicDriverInfo ").Replace("</PersDriver>","</BasicDriverInfo>")
            $RawXMLText = $RawXMLText.Replace("<Watercraft ","<BasicWatercraftInfo ").Replace("</Watercraft>","</BasicWatercraftInfo>")
        }
        # Name stuff
        if (($RawXMLText.Indexof('<FamilyNames>')) -gt 0) {
            $RawXMLText = $RawXMLText.Replace('<FamilyName>','<CommlName>').Replace('</FamilyName>','</CommlName>')
            $start = $RawXMLText.Indexof('<FamilyNames>')
            $end = $RawXMLText.Indexof('</Surname>',$start) 
            $StringToReplace = $RawXMLText.Substring($start,$end + 10 - $start)

            $name1Start = $start + 13
            $name1End = $RawXMLText.Indexof('</FamilyNames>',$name1Start) - 1
            $name1 = $RawXMLText.Substring($name1Start,$name1End + 1 - $name1Start)

            $name2Start = $RawXMLText.Indexof('<Surname>',$name1End) + 9
            $name2End = $RawXMLText.Indexof('</Surname>',$name2Start) - 1
            $name2 = $RawXMLText.Substring($name2Start,$name2End + 1 - $name2Start)
                
            $newName = "<CommercialName>$name1 $name2</CommercialName>"
            $newName = $newName.Replace(" AND "," &amp; ").Replace(" and "," &amp; ")
            $RawXMLText = $RawXMLText.Replace($StringToReplace,$newName)
            }
        # END Plain Text manipulation

        $XmlDocument = [xml]$RawXMLText
        $XmlDocument = Inject_StyleSheet_Link ($XmlDocument)

        # Clean up any invalid SSNs
        $SSNs = $XmlDocument.SelectNodes('//TaxIdentity')
        Foreach ($ssn in $SSNs) { if ((($ssn.TaxId).Trim()).Length -ne 9) { $ssn.TaxId = ''; $global:Counts['InvalidTaxIds']++ }}

        $PN = ($XmlDocument.SelectSingleNode("//PolicyNumber")).InnerXML

        $PoliciesToWatch = @( `
'US218323262', `
'US235330342', `
'US245971657'
)
    if ($PoliciesToWatch.Contains($PN)){
         write-host "here"
        }


        $LOBCd = LOBCd_From_ACORD_XML ($XmlDocument)

        # Check for AdditionalInterest nodes under PersPolicy and move them under Location
        if ($LOBCd -eq 'HOME') {
            $PersPolicy = $XmlDocument.SelectSingleNode('//PersPolicy')
            $AIs = $PersPolicy.SelectNodes('AdditionalInterest')
            $global:PersPolicyAdditionalInterests += $AIs.Count
            $global:Counts.PersPolicyAdditionalInterests += $AIs.Count
            $Location = $XmlDocument.SelectSingleNode('//Location')
            Foreach ($ai in $AIs) {
                [void] $Location.AppendChild($XmlDocument.ImportNode($ai, $true))
                [void] $PersPolicy.RemoveChild($ai)
                }
            }

        if ($LOBCd -eq 'AUTOP') { # add up coverages and inject per-vehicle premiums
            $PolicyPremium = 0
            $Vehicles = $XmlDocument.SelectNodes('//PersVeh')
            Foreach ($vehicle in $Vehicles) {
                # calculate per vehicle premium and inject new node
                $vehPrem = ($vehicle.SelectSingleNode("./FullTermAmt/Amt")).InnerXML
                if ( $vehPrem -eq $null ) {
                    $vehPrem = 0
                    #write-host "Calculating per-vehicle premium"
                    $prem = $vehicle.SelectNodes('./Coverage/CurrentTermAmt/Amt')
                    $prem | Foreach { $vehPrem += [int]$_.InnerText}
                    $vehPremText = [string]::Format("{0:000}", $vehPrem)
                    $newFullTermAmtNode = $vehicle.AppendChild($XmlDocument.CreateElement("FullTermAmt"));
                    $newFullTermAmtAmt = $newFullTermAmtNode.AppendChild($XmlDocument.CreateElement("Amt"));
                    $newFullTermAmtAmtNode = $newFullTermAmtAmt.AppendChild($XmlDocument.CreateTextNode($vehPremText));
                    #write-host "[$PolicyNo] Updated Vehicle Premium to $vehPremText."
                    $specialCharacter = $true
                    }
                $PolicyPremium += $vehPrem
             }
             $PolicyNode = $XmlDocument.SelectSingleNode("//PersPolicy")
             $CurrentPolicyPremium = $PolicyNode.CurrentTermAmt.Amt # .InnerText
             # Update the Policy Premium based on the sum of the individual Coverages
             if (($CurrentPolicyPremium -eq $null) -and ($PolicyPremium -gt 0)) {
                # NOTE: This was 'fixed' 09/13 but not tested
                Log -Str "NOTE: Updating policy premium for Policy Number $PN"
                $PolicyPremiumText = [string]::Format("{0:000}", $PolicyPremium)
                
                $newPolicyTermAmtNode = $PolicyNode.AppendChild($XmlDocument.CreateElement("FullTermAmt"));
                $newPolicyTermAmtAmt = $newPolicyTermAmtNode.AppendChild($XmlDocument.CreateElement("Amt"));
                $newFullTermAmtAmtNode = $newPolicyTermAmtAmt.AppendChild($XmlDocument.CreateTextNode($PolicyPremiumText));

                #$PolicyPremiumNode.InnerText = $PolicyPremiumText
                #write-host "[$PolicyNo] Updated Policy Premium to $PolicyPremiumText."
                }
            }

        # Add in the Safeco BookTransfer Metadata
        switch ($LOBCd) {
            "PPKGE" { $InqRq = "PersPkgPolicyQuoteInqRq" }
            "AUTOP" { $InqRq = "PersAutoPolicyQuoteInqRq" }
            "HOME"  { $InqRq = "HomePolicyQuoteInqRq" }
            "DFIRE" { $InqRq = "DwellFirePolicyQuoteInqRq" }
            "BOAT"  { $InqRq = "WatercraftPolicyQuoteInqRq" }
            "UMBRP" { $InqRq = "PersUmbrellaPolicyQuoteInqRq" }
            "INMRP" { $InqRq = "PersInlandMarinePolicyQuoteInqRq" }
            default { $InqRq = "" }
            }
        # Insert Safeco BookTransfer MetaData Node
        Add_Metadata -XMLDocument $XmlDocument -NodeName 'Source_Name' -Value 'IVANS Bookroll'

        if ($global:MOBILEHOMEOWNERS) { Add_Metadata -XMLDocument $XmlDocument -NodeName 'Original_LOBCd' -Value 'MHOME (Mobile Homeowners)' }
        # Check HOME and DFIRE and set flag for Manufactured Home
        $DwellNode = $XmlDocument.SelectSingleNode("//Dwell")
        if ($DwellNode.PolicyTypeCd -eq 'MH') {
            $DwellInspectionValuationNode = $DwellNode.DwellInspectionValuation
            if ($DwellInspectionValuationNode -eq $null) { $DwellInspectionValuationNode = $DwellNode.AppendChild($XmlDocument.CreateElement("DwellInspectionValuation")) }
            if ($DwellInspectionValuationNode.DwellStyleCd -eq $null) {
                $DwellStyleCdNode = $DwellInspectionValuationNode.AppendChild($XmlDocument.CreateElement("DwellStyleCd"))
                [void] $DwellStyleCdNode.AppendChild($XmlDocument.CreateTextNode('com.safeco_Manufactured'));
                }
            $DwellInspectionValuationNode.DwellStyleCd = 'com.safeco_Manufactured'
            Add_Metadata -XMLDocument $XmlDocument -NodeName 'Set_DwellStyleCd_Node' -Value 'com.safeco_Manufactured'
            }

        # START Name Report BEFORE
        $PrimaryInsured = $XmlDocument.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
        $CoInsured = $XmlDocument.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']")
        $itc=$itp=$ign=$ion=$isn=$isx=$citc=$citp=$cign=$cion=$cisn=$cisx=""
        if ($PrimaryInsured) {
            $itc = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName + '"' # Surround by quotes for CSV record
            $itp = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix + '"' # Surround by quotes for CSV record
            $ign = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + '"' # Surround by quotes for CSV record
            $ion = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName + '"' # Surround by quotes for CSV record
            $isn = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname + '"' # Surround by quotes for CSV record
            $isx = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix + '"' # Surround by quotes for CSV record
        }
        if ($CoInsured) {
            $citc = '"' + $CoInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName + '"' # Surround by quotes for CSV record
            $citp = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix + '"' # Surround by quotes for CSV record
            $cign = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + '"' # Surround by quotes for CSV record
            $cion = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName + '"' # Surround by quotes for CSV record
            $cisn = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.Surname + '"' # Surround by quotes for CSV record
            $cisx = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix + '"' # Surround by quotes for CSV record
        }
        $beforestring = "$itc,$itp,$ign,$ion,$isn,$isx,$citc,$citp,$cign,$cion,$cisn,$cisx"
        # END Name Report BEFORE

        $XmlDocument = Check_And_Adjust_Names $XmlDocument

        # START Name Report AFTER
        $PrimaryInsured = $XmlDocument.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
        try { $CoInsured = $XmlDocument.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']") }
        catch { $CoInsured = $null }
        if ($global:FLAG_Generate_Name_Report) {
            $itc=$itp=$ign=$ion=$isn=$isx=$citc=$citp=$cign=$cion=$cisn=$cisx=""
            if ($PrimaryInsured) {
                $itc = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName + '"' # Surround by quotes for CSV record
                $itp = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix + '"' # Surround by quotes for CSV record
                $ign = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + '"' # Surround by quotes for CSV record
                $ion = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName + '"' # Surround by quotes for CSV record
                $isn = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname + '"' # Surround by quotes for CSV record
                $isx = '"' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix + '"' # Surround by quotes for CSV record
            }
            if ($CoInsured) {
                $citc = '"' + $CoInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName + '"' # Surround by quotes for CSV record
                $citp = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix + '"' # Surround by quotes for CSV record
                $cign = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + '"' # Surround by quotes for CSV record
                $cion = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName + '"' # Surround by quotes for CSV record
                $cisn = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.Surname + '"' # Surround by quotes for CSV record
                $cisx = '"' + $CoInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix + '"' # Surround by quotes for CSV record
            }
            $afterstring = "AFTER,$LOBCd,$PN,$global:NameCase,$itc,$itp,$ign,$ion,$isn,$isx,$citc,$citp,$cign,$cion,$cisn,$cisx"

            $beforestring = "BEFORE,$LOBCd,$PN,$global:NameCase," + $beforestring
            Add-Content -Path $global:ReportFile -Value $beforestring
            Add-Content -Path $global:ReportFile -Value $afterstring
            $global:NameCase = "" # reset it for the next one
        }
        # END Name Report AFTER

        $XmlDocument = Inject_AgencyID -XmlDocument $XmlDocument -BTRecord $BT_Record

        if ($LOBCd -eq 'PPKGE') {
            Split_Package_XML_File -XmlDocument $XmlDocument -XMLFolder $XMLFolder -DestinationFolder $DestinationFolder
            } 
        else {
            $newName = Filename_From_XML $XmlDocument
            $newName = Join-Path -Path $DestinationFolder -ChildPath $newName
            if (Test-Path -Path $newName) { $duplicates++ }
            if ($newName -ne "") { $XmlDocument.Save($newName) }

            # Now, add to list for Inland Marine and Home
            $CustomerID = $XmlDocument.SelectSingleNode($global:AgencyId).InnerText
            $PolicyNumber = ($XmlDocument.SelectSingleNode($global:PolicyNumberString)).InnerXML
            # Remember Inland Marine and Home policies to merge later
            if ($LOBCd -eq 'INMRP') { 
                $Locations = ""
                $locs = $XmlDocument.SelectNodes('//Location')
                foreach ($l in $locs) { $l.id = "im_"+$l.id; $Locations += $l.OuterXML }
                $Dwells = ""
                $dwls = $XmlDocument.SelectNodes('//Dwell')
                foreach ($d in $dwls) { $d.id = "im_"+$d.id; $d.LocationRef = "im_"+$d.LocationRef; $Dwells += $d.OuterXML }
                $ScheduledProperties = ""
                $properties = $XmlDocument.SelectNodes('//PropertySchedule')
                foreach ($p in $properties) { $p.LocationRef = "im_"+$p.LocationRef; $ScheduledProperties += $p.OuterXML }
                $global:InlandMarinePolicies += (@{ 
						    CustomerID = $CustomerID
						    Filename = $newName
                            # Grab the XML data and elements that will be merged with the matching Home
                            PolicyNumber = $PolicyNumber
                            PolicyPremium = ($XmlDocument.SelectSingleNode('//PersPolicy/CurrentTermAmt/Amt')).InnerXML
                            Coverage = $XmlDocument.SelectSingleNode('//Coverage').OuterXML
                            Locations = $Locations
                            Dwells = $Dwells
                            ScheduledProperties = $ScheduledProperties
                            })
                }
            if ($LOBCd -eq 'HOME') {
                $global:HomePolicies += (@{ 
						    CustomerID = $CustomerID
                            PolicyNumber = $PolicyNumber
						    Filename = $newName
                    }) 
                }

            
            $i++
            if (($i % 10) -eq 0) { write-host ([string]::Format("{0:000}", $i)) -ForegroundColor Red -NoNewline }
            else { 
                if ($specialCharacter) { $char = "*"; $color = 'White' } else { $char = "."; $color = 'Green'}
                write-host $char -ForegroundColor $color -NoNewline 
                }
            if (($i % 100) -eq 0) { write-host "" }
            $specialCharacter = $false
            }
        }
    write-host
    if ($duplicates -gt 0) { Log -Str "NOTE: Duplicate policies found: $duplicates" }
}
######################################################################################################################


######################################################################################################################
Function Number_Of_Policies ($XmlDocument) {
    # Return the number of policies found in XmlDocument, based on the number of PersPolicy nodes (non-LOB-specific)
    $NumberOfPolicies = 0
    $pols = $XmlDocument.SelectNodes("//PersPolicy")
    if ($pols -eq $null) {
        write-host "ERROR: this file does not appear to be an ACORD XML with PersPolicy node."
        } else {
            $NumberOfPolicies = $pols.Count
#            write-host "this file contains $NumberOfPolicies policies"
            }
    return $NumberOfPolicies
}
######################################################################################################################
Function Is_Single_Policy ($XmlDocument) {
    return ((Number_Of_Policies($XmlDocument)) -eq 1)
}


######################################################################################################################
Function XML_Skeleton ($LOB) {
$stylesheetName = ""
    switch ($LOB) {
        "AUTO"  { $stylesheetName = "AutoXMLView.xslt" }
        "AUTOP" { $stylesheetName = "AutoXMLView.xslt" }
        "HOME"  { $stylesheetName = "HomeXMLView.xslt" }
        "DFIRE" { $stylesheetName = "DFireXMLView.xslt" }
        "BOAT"  { $stylesheetName = "BoatXMLView.xslt" }
        "UMBRP" { $stylesheetName = "UmbXMLView.xslt" }
        "PUMBR" { $stylesheetName = "UmbXMLView.xslt" }
        "UMBRL" { $stylesheetName = "UmbXMLView.xslt" }
        "INMRP" { $stylesheetName = "InlandMarineXMLView.xslt" }
        default { $stylesheetName = "" }
        }

    return [xml]@"
<?xml version='1.0' encoding='UTF-8' standalone='no'?>
<?xml-stylesheet type='text/xsl' href='$stylesheetName'?>
<ACORD>
</ACORD>
"@
}

######################################################################################################################
Function XML_InsuranceSvcRq {
    $guid = New-Guid                    
    return [xml]@"
<InsuranceSvcRq>
<RqUID>$guid</RqUID>
</InsuranceSvcRq>
"@
}
######################################################################################################################
Function Initialize_ACORD_XML_Report() {
    $global:SplitReportFile = Join-Path -Path $global:selectedFolder -ChildPath ("Split ACORD XML Report," + [DateTime]::Now.ToString("yyyyMMdd-HHmmss") + ".csv")

    Add-Content -Path $global:SplitReportFile  -Value '"No.","Folder","Filename","Policy Number","Contract Number","Eff Dt","Prior Prem","LOB","NAICCd","Insured Name","Insured DOB","CoInsured Name","CoInsured DOB","State","# Drivers","# Vehicles","# PropertySchedule","Policy Form No.","# Forms"'
    $global:SplitCounter  = 0
 }

######################################################################################################################
# Functions to return specific values from a single ACORD XML Policy
# NOTE: No check is made for multiple policies in the string so these should only be called with a known single policy
Function PolicyNumber_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode($global:PolicyNumberString)).InnerXML
}
Function ExpirationDate_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//PersPolicy/ContractTerm/ExpirationDt")).InnerXML
}
Function PriorPremium_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//PersPolicy/CurrentTermAmt/Amt")).InnerXML
}
Function LOBCd_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode($global:LOBCdString)).InnerXML
}
Function NAICCd_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//PersPolicy/NAICCd")).InnerXML
}
Function Form_Number_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//HomeLineBusiness/Dwell/PolicyTypeCd")).InnerXML
}



Function Inject_CommercialName_Into_ACORD_XML ($ACORD_XML) {
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    if ($CommercialName -eq $null) {
        $CommercialName = CommercialName_From_ACORD_XML ($ACORD_XML)
        $CommlNameNode = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName
        if ($CommlNameNode -eq $null) {
            $CommlNameNode = ($PrimaryInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement("CommlName"));
            }
        $CommercialNameNode = $CommlNameNode.CommercialName
        if ($CommercialNameNode -eq $null) {
            $CommercialNameNode = $CommlNameNode.AppendChild($ACORD_XML.CreateElement("CommercialName"));
            }
        [void] $CommercialNameNode.AppendChild($ACORD_XML.CreateTextNode($CommercialName));
        }
    return $ACORD_XML
}
Function CommercialName_From_ACORD_XML ($ACORD_XML) {
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    if ($CommercialName -eq $null) {
        $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
        $CoInsuredName = CoInsuredName_From_ACORD_XML $ACORD_XML
        if (($CommercialName.IndexOf('&amp;') -lt 0) -and ($CoInsuredName -ne '')) { 
            $CommercialName = $CommercialName + ' &amp; ' + (CoInsuredName_From_ACORD_XML $ACORD_XML) 
            }
        }
    return $CommercialName
}
Function InsuredName_From_ACORD_XML ($ACORD_XML) {
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    if ($InsuredName -eq $null) {
        $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
        }
    if ($InsuredName -eq $null) {
        $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.FamilyNames + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.Surname
        }
    if ($InsuredName -eq $null) {
        $InsuredName = "NO NAME FOUND"
        }
    $InsuredName = $InsuredName.Trim()
    return $InsuredName
}
Function CoInsuredName_From_ACORD_XML ($ACORD_XML) {
    $CoInsuredName = ''
    $CoInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']")
    if ($CoInsured -ne $null) {
        $CoInsuredName = $CoInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
        if ($CoInsuredName -eq $null) {
            $CoInsuredName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
            }
        if ($CoInsuredName -eq $null) {
            $CoInsuredName = $CoInsured.GeneralPartyInfo.NameInfo.FamilyName.FamilyNames + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.Surname
            }
        if ($CoInsuredName -eq $null) {
            $CoInsuredName = "NO NAME FOUND"
            }
    }
    return $CoInsuredName.Trim()
}
Function Insured_DOB_From_ACORD_XML ($ACORD_XML) {
    $Insured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $DOB = $Insured.InsuredOrPrincipalInfo.PersonInfo.BirthDt
    return $DOB
}
Function CoInsured_DOB_From_ACORD_XML ($ACORD_XML) {
    $CoInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']")
    $DOB = $CoInsured.InsuredOrPrincipalInfo.PersonInfo.BirthDt
    return $DOB
}
Function MailingState_From_ACORD_XML ($ACORD_XML) {
    $MailingState = ($ACORD_XML.SelectSingleNode("//InsuredOrPrincipal/GeneralPartyInfo/Addr/StateProvCd")).InnerXML
    return $RatingState
}
Function RatingState_From_ACORD_XML ($ACORD_XML) {
    $RatingState = ($ACORD_XML.SelectSingleNode("//Location/Addr/StateProvCd")).InnerXML
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//InsuredOrPrincipal/GeneralPartyInfo/Addr/StateProvCd")).InnerXML
    }
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//PersPolicy/ControllingStateProvCd")).InnerXML
    }
    return $RatingState
}
Function PriorCarrier_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode($global:PolicyNumberString)).InnerXML
}
Function AgencyID_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//AgencyId")).InnerXML
}
Function ContractNumber_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//ProducerInfo/ContractNumber")).InnerXML
}
Function PriorCarrier_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode("//ClientApp/Name")).InnerXML
}

Function Generate_CommercialName_From_ACORD_XML ($ACORD_XML) {
# $ACORD_XML may be a document or may be a policy node within a document, so we check for both
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    if ($PrimaryInsured -eq $null) {       
        $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
        }
    if ($PrimaryInsured -eq $null) {
        return ""
        }
    # at this point, $PrimaryInsured points to a valid InsuredOrPrincipal node
    $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    if ($CommercialName -ne $null) {
        return $CommercialName.Trim()
        }
    # Insured Commercial Name
    $itp = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix
    $ign = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName
    $ion = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName
    $isn = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
    $isx = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix
    $InsuredCommercialName = $itp + " " + $ign + " " + $ion + " " + $isn + " " + $isx
    $InsuredCommercialName = $InsuredCommercialName.Replace("  ", " ").Trim()
    if ($InsuredCommercialName -eq "") {
        $InsuredCommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.FamilyNames + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.Surname
        $InsuredCommercialName = $InsuredCommercialName.Replace("  ", " ").Trim()
        }

    $CoInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']")
    if ($CoInsured -ne $null) {
        # Coinsured Commercial Name
        $citp = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.TitlePrefix
        $cign = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName
        $cion = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName
        $cisn = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
        $cisx = $CoInsured.GeneralPartyInfo.NameInfo.PersonName.NameSuffix
        $CoInsuredCommercialName = $citp + " " + $cign + " " + $cion + " " + $cisn + " " + $cisx
        $CoInsuredCommercialName = $CoInsuredCommercialName.Replace("  ", " ").Trim()
        if ($CoInsuredCommercialName -eq "") {
            $CoInsuredCommercialName = $CoInsured.GeneralPartyInfo.NameInfo.FamilyName.FamilyNames + ' ' + $CoInsured.GeneralPartyInfo.NameInfo.FamilyName.Surname
            $CoInsuredCommercialName = $CoInsuredCommercialName.Replace("  ", " ").Trim()
            }
       }
    $connector = ""
    if (($CoInsuredCommercialName -ne $null) -and ($CoInsuredCommercialName -ne "")) {
        $connector = " & "
        if ($isn -eq $cisn) {
            # same last names
            if ($isx -eq $null) {
                # PrimaryInsured doesn't have a Suffix
                $InsuredCommercialName = $itp + " " + $ign + " " + $ion
                }
            }
        }
    $CommercialName = $InsuredCommercialName + $connector + $CoInsuredCommercialName
    $CommercialName = $CommercialName.Replace("  ", " ").Replace("&", "&amp;")
    return $CommercialName.Trim()
}

######################################################################################################################
Function Add_To_SplitSpreadsheet_Report () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XmlDocument,       
        [Parameter(Mandatory=$true)]
        $Folder,       
        [Parameter(Mandatory=$true)]
        $Filename
        )
        
    if ($global:SplitReportFile -ne $null) {
        $ContractNumber = ContractNumber_From_ACORD_XML ($XmlDocument)
        $Effective_Date = ExpirationDate_From_ACORD_XML ($XmlDocument)
        $Prior_Premium = PriorPremium_From_ACORD_XML ($XmlDocument)
        $LOBCd = LOBCd_From_ACORD_XML ($XmlDocument)
        $NAICCd = NAICCd_From_ACORD_XML ($XmlDocument)
        $Insured_Name = InsuredName_From_ACORD_XML ($XmlDocument)
        $Insured_Name = $Insured_Name.replace(","," ")
        $CoInsured_Name = CoInsuredName_From_ACORD_XML ($XmlDocument)
        $CoInsured_Name = $CoInsured_Name.replace(","," ")
        $Insured_DOB = Insured_DOB_From_ACORD_XML ($XmlDocument)
        $CoInsured_DOB = CoInsured_DOB_From_ACORD_XML ($XmlDocument)
        $Policy_Number = PolicyNumber_From_ACORD_XML ($XmlDocument)
        $State = RatingState_From_ACORD_XML ($XmlDocument)

        $PropertySchedule = $XmlDocument.SelectNodes('//PropertySchedule')
        $NumPropertySchedule = $PropertySchedule.Count

        $Drivers = $XmlDocument.SelectNodes('//PersDriver')
        $NumDrivers = $Drivers.Count
        $Vehicles = $XmlDocument.SelectNodes('//PersVeh')
        $NumVehicles = $Vehicles.Count

        $PolicyFormNo = Form_Number_From_ACORD_XML ($XmlDocument)
        $Forms = $XmlDocument.SelectNodes('//Form')
        $NumForms = $Forms.Count

        $fldr = '"' + $Folder + '"' # Surround by quotes for CSV record
        $fn = '"' + $Filename + '"' # Surround by quotes for CSV record
        $global:SplitCounter++
        $outRecord = "$global:SplitCounter,$fldr,$fn,$Policy_Number,$ContractNumber,$Effective_Date,$Prior_Premium,$LOBCd,$NAICCd,$Insured_Name,$Insured_DOB,$CoInsured_Name,$CoInsured_DOB,$State,$NumDrivers,$NumVehicles,$NumPropertySchedule,$PolicyFormNo,$NumForms"

        Add-Content -Path $global:SplitReportFile  -Value $outRecord
    }
}
######################################################################################################################
function global:prompt {  # Multiple Write-Host commands with color
    Write-Host("[") -nonewline
    Write-Host((Get-Date).ToShortTimeString()) -nonewline -foregroundcolor Red
    Write-Host("] ") -nonewline
    Write-Host($(Split-Path $pwd -Leaf)) -nonewline -foregroundcolor White
    return " > "
}
######################################################################################################################
Function Unique_Filename ($fullpath) {
    $uniquepath = $fullpath
    if (Test-Path -Path $uniquepath) {
        $dir = Split-Path $uniquepath -Parent
        $basename = [System.IO.Path]::GetFileNameWithoutExtension("$uniquepath")
        $extension = [System.IO.Path]::GetExtension("$uniquepath")
        for ($i = 2; (Test-Path -Path $uniquepath); $i++) {
            $uniquepath = Join-Path -Path $dir -ChildPath (-join("$basename", " ($i)", "$extension"))
        }
    }
#    return [System.IO.DirectoryInfo] $uniquepath
    return $uniquepath
}
######################################################################################################################
# Return a filename based on the contents of the provided ACORD XML Policy String
Function Filename_From_XML_String ($ACORD_XML_STRING) {
    return (Filename_From_XML ([xml]$ACORD_XML_STRING))
}
######################################################################################################################
Function InsuredName_From_ACORD_XML ($ACORD_XML) {
    $PrimaryInsured = $ACORD_XML.SelectSingleNode($global:InsuredString)
    $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
    if ($InsuredName -eq $null) {
        $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.Surname
        }
    if ($InsuredName -eq $null) {
        $InsuredName = $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.FamilyNames + ' ' + $PrimaryInsured.GeneralPartyInfo.NameInfo.FamilyName.Surname
        }
    if ($InsuredName -eq $null) {
        $InsuredName = "NO NAME FOUND"
        }
    $InsuredName = $InsuredName.Trim()
    return $InsuredName
}
######################################################################################################################
# Return a filename based on the contents of the provided ACORD XML Policy String
Function Filename_From_XML ($ACORD_XML) {
    $RatingState = ($ACORD_XML.SelectSingleNode("//Location/Addr/StateProvCd")).InnerXML
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//InsuredOrPrincipal/GeneralPartyInfo/Addr/StateProvCd")).InnerXML
    }
    if ($RatingState -eq $null) {
        $RatingState = ($ACORD_XML.SelectSingleNode("//PersPolicy/ControllingStateProvCd")).InnerXML
    }
    $LOB = ($ACORD_XML.SelectSingleNode($global:LOBCdString)).InnerXML
    $PolicyNumber = ($ACORD_XML.SelectSingleNode($global:PolicyNumberString)).InnerXML
    $InsuredName = InsuredName_From_ACORD_XML ($ACORD_XML)
    $ExpDate = ($ACORD_XML.SelectSingleNode("//PersPolicy/ContractTerm/ExpirationDt")).InnerXML
    $legalFilename = "$LOB,$PolicyNumber,$RatingState,$InsuredName,$ExpDate.xml"
    $legalFilename = $legalFilename.Replace("  "," ")
    $legalFilename = $legalFilename -replace ("[{0}]"-f (([System.IO.Path]::GetInvalidFileNameChars()|%{[regex]::Escape($_)}) -join '|')),'_'
    return $legalFilename
}

######################################################################################################################
Function Select_File() {
    Param(        
        [Parameter(Mandatory=$true)]
        $prompt,

        [Parameter(Mandatory=$false)]
        $filter = "all files (*.*)|*.*"
        )

    $selectedFile = ""
    [System.Windows.Forms.OpenFileDialog]$openFileDialog1 = New-Object System.Windows.Forms.OpenFileDialog
    $openFileDialog1.Title = $prompt
    $openFileDialog1.Filter = $filter 
    $openFileDialog1.InitialDirectory = $global:selectedFolder
    $openFileDialog1.FilterIndex = 1
    $openFileDialog1.RestoreDirectory = $true
	$openFileDialog1.MultiSelect = $false

    if ($openFileDialog1.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) {
        $selectedFile = $openFileDialog1.FileName
    }
    return $selectedFile
}

######################################################################################################################
######################################################################################################################
Function File_As_Clean_XML ($fullpath) {
    # This function removes all whitespace from the given XML file and returns it as XML

    $TheXML = (Get-content -Path $fullpath -Encoding UTF8 -raw)
#    ReportLength("Original:",$TheXML.length)
    $prevLen = $TheXML.length
    for ($len = 0; $len -ne $prevLen; $len = $TheXML.length) {
        $prevLen = $TheXML.length            
        $TheXML = $TheXML.replace("  "," ")
    }
#    ReportLength("Remove double spaces:",$TheXML.length)

     return [xml]$TheXML
}
######################################################################################################################
Function PolicyNumber_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode($global:PolicyNumberString)).InnerXML
}
Function LOBCd_From_ACORD_XML ($ACORD_XML) {
    return ($ACORD_XML.SelectSingleNode($global:LOBCdString)).InnerXML
}
######################################################################################################################

######################################################################################################################
Function Inject_StyleSheet_Link ($XmlDocument) { 

$pattern = "XXXXXXXXXX"
$xmlLine = "<?xml version='1.0' encoding='UTF-8' standalone='no'?>"
$stylesheetLine = "<?xml-stylesheet type='text/xsl' href='$pattern'?>"
$styleSheetHeader = "<?xml version='1.0' encoding='UTF-8' standalone='no'?>$NL<?xml-stylesheet type='text/xsl' href='$pattern'?>"
$header = ""
$rootDirectory = ""
$styleSheetFilename = ""

    # now make an attempt to inject the stylesheet link
    $DocumentAsString = $XmlDocument.OuterXml
    $newXMLDocument = $XmlDocument
    # first, check to see whether it already has a stylesheet link
    if ($DocumentAsString.IndexOf("<?xml-stylesheet type='text/xsl' href=") -ge 0) {
        if ($global:DEBUG) { write-host "$i/$totalFiles) $f already contains a stylesheet link, skipping." }
        } 
    else {
        switch (LOBCd_From_ACORD_XML($XmlDocument)) {
            "AUTO"  { $styleSheetFilename = "PL_AUTOP_STYLESHEET.xslt" }
            "AUTOP" { $styleSheetFilename = "PL_AUTOP_STYLESHEET.xslt" }
            "HOME"  { $styleSheetFilename = "PL_HOME_STYLESHEET.xslt"  }
            "DFIRE" { $styleSheetFilename = "PL_DFIRE_STYLESHEET.xslt" }
            "BOAT"  { $styleSheetFilename = "PL_BOAT_STYLESHEET.xslt"  }
            "UMBRP" { $styleSheetFilename = "PL_UMBRP_STYLESHEET.xslt" }
            "UMBRL" { $styleSheetFilename = "PL_UMBRP_STYLESHEET.xslt" }
            "INMAR" { $styleSheetFilename = "PL_INMRP_STYLESHEET.xslt" }
            "INMRP" { $styleSheetFilename = "PL_INMRP_STYLESHEET.xslt" }
            default { $styleSheetFilename = "" }
            }
        if ($styleSheetFilename -ne "") {
            $newpath = $styleSheetFilename
            if ($rootDirectory -ne "") {
                $newpath = Join-Path -Path $rootDirectory -ChildPath $styleSheetFilename
                }
            # check to see if the file already has an xml line
            if ($XmlDocument.xml -ne $null) {
                if ($global:DEBUG) { write-host "$i/$totalFiles) $f already contains an xml line so will only add stylesheet line." }
                $theXmlLine = '<?xml ' + $XmlDocument.xml.ToString() + '?>'
                $actualStylesheetLine = $stylesheetLine.Replace($pattern,$newpath)
                $newDocString = ($XmlDocument.OuterXml).Replace($theXmlLine,"$xmlLine$NL$actualStylesheetLine")
                [xml]$newXMLDocument = [xml]$newDocString
                }
            else {  
                if ($global:DEBUG) { write-host "adding both xml line and stylesheet line." }
                $header = $styleSheetHeader.Replace($pattern,$newpath)         
                [xml]$newXMLDocument = [xml]($header + $NL + $XmlDocument.OuterXml)
                }
            if ($global:DEBUG) { write-host "now has a stylesheet link for $lobText policies." }
        }
        else {
            if ($global:DEBUG) { write-host "unable to insert a stylesheet link" }
            }
        }
    return $newXMLDocument
}
######################################################################################################################
Function IOP_Fullname ($InsuredOrPrincipal) {
    $CommercialNameText = $InsuredOrPrincipal.GeneralPartyInfo.NameInfo.PersonName.GivenName + 
        " " + $InsuredOrPrincipal.GeneralPartyInfo.NameInfo.PersonName.OtherGivenName + 
        " " + $InsuredOrPrincipal.GeneralPartyInfo.NameInfo.PersonName.Surname
    return $CommercialNameText
}
######################################################################################################################
Function Same_Names ($FirstIOP,$SecondIOP) {
return ($FirstIOP.GeneralPartyInfo.NameInfo.PersonName.Surname -eq $SecondIOP.GeneralPartyInfo.NameInfo.PersonName.Surname) -and
   ($FirstIOP.GeneralPartyInfo.NameInfo.PersonName.GivenName -eq $SecondIOP.GeneralPartyInfo.NameInfo.PersonName.GivenName)
}
######################################################################################################################


######################################################################################################################
######################################################################################################################
Function XML_String_With_SingonRq () {
    Param(        
        [Parameter(Mandatory=$true)]
        $SignonRqString
        )

    $guid = New-Guid
    return [xml]@"
<ACORD>
    $SignonRqString
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
}
######################################################################################################################
Function Inject_AgentBookRollInfo () {
    Param(
        [Parameter(Mandatory=$true)]
        $ACORD_XML,
        [Parameter(Mandatory=$true)]
        $BTCode
        )

    $CampaignId = $ACORD_XML.ACORD.AgentBookRollInfo.CarrierPortalInfo.CarrierInfo.CampaignId
    if ($CampaignId -eq $null) {
        $AgentBookRollInfoNode = ($ACORD_XML.ACORD).AppendChild($ACORD_XML.CreateElement("AgentBookRollInfo"))
        $CarrierPortalInfoNode = $AgentBookRollInfoNode.AppendChild($ACORD_XML.CreateElement("CarrierPortalInfo"))
        $CarrierInfoNode = $CarrierPortalInfoNode.AppendChild($ACORD_XML.CreateElement("CarrierInfo"))
        $CampaignIdNode = $CarrierInfoNode.AppendChild($ACORD_XML.CreateElement("CampaignId")) 
        [void] $CampaignIdNode.AppendChild($ACORD_XML.CreateTextNode($BTCode));
        $UserNameNode = $CarrierInfoNode.AppendChild($ACORD_XML.CreateElement("UserName")) # this node remains empty
        }
    else {
        if ($CampaignId -ne $BTCode) {
            $ACORD_XML.ACORD.AgentBookRollInfo.CarrierPortalInfo.CarrierInfo.CampaignId = "$BTCode"
        }
    }
    return $ACORD_XML
}
######################################################################################################################
######################################################################################################################
Function Combine_XML_Files () {
#Combines XML files of like LOB into single file for uploading to AQE
    Param(        
        [Parameter(Mandatory=$true)]
        $SourceFolder,       
        [Parameter(Mandatory=$true)]
        $XMLDestinationFolder,
        [Parameter(Mandatory=$false)]
        $BTRecord = ""
        )    

    $MaxPerFile = 1000
    $a = 0; $h = 0; $im = 0; $d = 0; $b = 0; $u = 0; $unk = 0; $skip = 0; $i = 0
    $aTotal = 0; $hTotal = 0; $dTotal = 0; $bTotal = 0; $uTotal = 0; 
    $aPart = 0; $hPart = 0; $dPart = 0; $bPart = 0; $uTotal = 0; 

    $gotSignOn = $false
    $AutoDoc = $null; $BoatDoc = $null; $HomeDoc = $null; $DwellingDoc = $null; $UmbrellaDoc = $null

    $totalFiles = (Get-ChildItem -Path $SourceFolder -Filter *.xml -File).count
    
    if (!(Test-Path -Path $XMLDestinationFolder)) { [void](New-Item -Path $XMLDestinationFolder -ItemType "Directory") }

    Get-ChildItem -Path $SourceFolder -Filter *.xml -File  | Sort name  | Foreach-Object {
        $specialCharacter = "?"
        $f = $_.BaseName
        $XmlDocument = [xml](Get-Content -Path $_.FullName)

        if (-not $gotSignOn) {
            # grab the first file and get the SignonRq node to create the template
            $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
            $SignonRqText = $SignonRq.OuterXML
            $guid = New-Guid
            $template = [xml]@"
<ACORD>
	$SignonRqText
    <InsuranceSvcRq>
        <RqUID>$guid</RqUID>
    </InsuranceSvcRq>
</ACORD>
"@
        $gotSignOn = $true
        }

    $pols = $XmlDocument.SelectNodes("//PersPolicy")
 
        if ($pols.Count -eq 1) {
            $thisLOB = LOBCd_From_ACORD_XML($XmlDocument)
            switch ($thisLOB) {
                "AUTOP" {
                            if ($AutoDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $AutoDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                            }
                            if ($a -lt $MaxPerFile) {
                                $a++
                                [void]($AutoDoc.ACORD.InsuranceSvcRq).AppendChild($AutoDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersAutoPolicyQuoteInqRq"), $true))
                                #write-host "$a AUTOP"
                                $specialCharacter = "a"
                                }
                            else {
                                $aTotal += $a; $aPart++;
                                $AutoDoc = Inject_AgentBookRollInfo -ACORD_XML $AutoDoc -BTCode $BTRecord
                                if ($aPart -gt 0) { $filenum = "$aPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--AUTOP Combined $filenum($a PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $AutoDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Auto file contains $a policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $a = 1; 
                                $AutoDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($AutoDoc.ACORD.InsuranceSvcRq).AppendChild($AutoDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersAutoPolicyQuoteInqRq"), $true))
                                $specialCharacter = "a"
                            }
                        }
                "HOME"  { 
                            if ($HomeDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $HomeDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($h -lt $MaxPerFile) {
                                $h++
                                [void]($HomeDoc.ACORD.InsuranceSvcRq).AppendChild($HomeDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq"), $true))   
                                #write-host "$h HOME"
                                $specialCharacter = "h"
                                }
                            else {
                                $hTotal += $h; $hPart++;
                                $HomeDoc = Inject_AgentBookRollInfo -ACORD_XML $HomeDoc -BTCode $BTRecord
                                if ($hPart -gt 0) { $filenum = "$hPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--HOME Combined $filenum($h PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $HomeDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Home file contains $h policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $h = 1; 
                                $HomeDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($HomeDoc.ACORD.InsuranceSvcRq).AppendChild($HomeDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/HomePolicyQuoteInqRq"), $true))
                                $specialCharacter = "h"
                            }                         
                        }
                "DFIRE" { 
                            if ($DwellingDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $DwellingDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($d -lt $MaxPerFile) {
                                $d++
                                [void]($DwellingDoc.ACORD.InsuranceSvcRq).AppendChild($DwellingDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/DwellFirePolicyQuoteInqRq"), $true))
                                #write-host "$d DFIRE"
                                $specialCharacter = "d"
                                }
                            else {
                                $dTotal += $d; $dPart++;
                                $DwellingDoc = Inject_AgentBookRollInfo -ACORD_XML $DwellingDoc -BTCode $BTRecord
                                if ($dPart -gt 0) { $filenum = "$dPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--DFIRE Combined $filenum($d PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $DwellingDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Dwelling file contains $d policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $d = 1; 
                                $DwellingDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($DwellingDoc.ACORD.InsuranceSvcRq).AppendChild($DwellingDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/DwellFirePolicyQuoteInqRq"), $true))
                                $specialCharacter = "d"
                            }
                        }
                "BOAT"  { 
                            if ($BoatDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $BoatDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($b -lt $MaxPerFile) {
                                $b++
                                [void]($BoatDoc.ACORD.InsuranceSvcRq).AppendChild($BoatDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/WatercraftPolicyQuoteInqRq"), $true))
                                #write-host "$b BOAT"
                                $specialCharacter = "b"
                                }
                            else {
                                $bTotal += $b; $bPart++;
                                $BoatDoc = Inject_AgentBookRollInfo -ACORD_XML $BoatDoc -BTCode $BTRecord
                                if ($bPart -gt 0) { $filenum = "$bPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--BOAT Combined $bPart($b PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $BoatDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Watercraft file contains $b policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $b = 1; 
                                $BoatDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($BoatDoc.ACORD.InsuranceSvcRq).AppendChild($BoatDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/WatercraftPolicyQuoteInqRq"), $true))
                                $specialCharacter = "b"
                           }
                        }
                "UMBRP" {
                            if ($UmbrellaDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $UmbrellaDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($u -lt $MaxPerFile) {
                                $u++
                                [void]($UmbrellaDoc.ACORD.InsuranceSvcRq).AppendChild($UmbrellaDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersUmbrellaPolicyQuoteInqRq"), $true)) 
                                #write-host "$u UMBRP"
                                $specialCharacter = "u"
                                }
                            else {
                                $uTotal += $u; $uPart++;
                                $UmbrellaDoc = Inject_AgentBookRollInfo -ACORD_XML $UmbrellaDoc -BTCode $BTRecord
                                if ($uPart -gt 0) { $filenum = "$uPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--UMBRP Combined $filenum($u PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $UmbrellaDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Umbrella file contains $u policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $u = 1; 
                                $UmbrellaDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($UmbrellaDoc.ACORD.InsuranceSvcRq).AppendChild($UmbrellaDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersUmbrellaPolicyQuoteInqRq"), $true))
                                $specialCharacter = "u"
                            }                          
                        }
                "PUMBR" {
                            if ($UmbrellaDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $UmbrellaDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($u -lt $MaxPerFile) {
                                $u++
                                [void]($UmbrellaDoc.ACORD.InsuranceSvcRq).AppendChild($UmbrellaDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersUmbrellaPolicyQuoteInqRq"), $true)) 
                                #write-host "$u PUMBR"
                                $specialCharacter = "u"
                                }
                            else {
                                $uTotal += $u; $uPart++;
                                $UmbrellaDoc = Inject_AgentBookRollInfo -ACORD_XML $UmbrellaDoc -BTCode $BTRecord
                                if ($uPart -gt 0) { $filenum = "$uPart " } else { $filenum = "" }
                                $filebasename = "IVANS BOOKROLL--UMBRP Combined $filenum($u PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $UmbrellaDoc.save($fname)
                                Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
                                Log -Str "$filebasename --> Umbrella file contains $u policies."
                                Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)."
                                # reset document and add $XMLDocument to it
                                $u = 1; 
                                $UmbrellaDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($UmbrellaDoc.ACORD.InsuranceSvcRq).AppendChild($UmbrellaDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersUmbrellaPolicyQuoteInqRq"), $true))
                                $specialCharacter = "u"
                            }                          
                        }
                "INMRP" {
                            if ($InlandDoc -eq $null) {
                                $SignonRqText = ""
                                $SignonRq = $XmlDocument.SelectSingleNode("ACORD/SignonRq")
                                if ($SignonRq -ne $null) { $SignonRqText = $SignonRq.OuterXML }
                                $InlandDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                }
                            if ($im -lt $MaxPerFile) {
                                $im++
                                [void]($InlandDoc.ACORD.InsuranceSvcRq).AppendChild($InlandDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersInlandMarinePolicyQuoteInqRq"), $true)) 
                                #write-host "$im INMRP"
                                $specialCharacter = "i"
                                }
                            else {
                                $iTotal += $i; $iPart++;
                                if ($iPart -gt 0) { $filenum = "$iPart " } else { $filenum = "" }
                                $filebasename = "MANUAL QUOTE--INMRP Combined $filenum($i PIF BT-$BTRecord).xml"
                                $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
                                $InlandDoc.save($fname)
                                Log -Str "$filebasename --> Inland Marine file contains $i policies." -SkipLog
                                # reset document and add $XMLDocument to it
                                $i = 1; 
                                $InlandDoc = XML_String_With_SingonRq -SignonRqString $SignonRqText
                                [void]($InlandDoc.ACORD.InsuranceSvcRq).AppendChild($InlandDoc.ImportNode($XmlDocument.SelectSingleNode("ACORD/InsuranceSvcRq/PersInlandMarinePolicyQuoteInqRq"), $true))
                                $specialCharacter = "i"
                            }                          
                        }
                default {
                            $unk = $unk + 1
                            #write-host "$unk UNKNOWN"
                            $specialCharacter = "?"
                        }
                }
            } else {
                $skip = $skip + 1
                #write-host "Skipping file ($f) because there are $cnt nodes in that file"
            }

        $i++
        if (($i % 10) -eq 0) { write-host ([string]::Format("{0:000}", $i)) -ForegroundColor Red -NoNewline }
        else { write-host $specialCharacter -ForegroundColor 'Green' -NoNewline }
        if (($i % 100) -eq 0) { write-host }
        $specialCharacter = ""
        }

    # now we clean up the docs
    write-host
    # write-host; write-host "Policy counts: $a AUTOP, $h HOME, $b BOAT, $d DFIRE, $u UMBRP, $im INMRP, $unk UNKNOWN, $skip SKIPPED."
                
    if ($a -gt 0) {
        $aPart++
        $AutoDoc = Inject_AgentBookRollInfo -ACORD_XML $AutoDoc -BTCode $BTRecord
        if ($aPart -gt 0) { $filenum = "$aPart " } else { $filenum = "" }
        $filebasename = "IVANS BOOKROLL--AUTOP Combined $filenum($a PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $AutoDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> Auto file contains $a policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($h -gt 0) {
        $hPart++
        $HomeDoc = Inject_AgentBookRollInfo -ACORD_XML $HomeDoc -BTCode $BTRecord
        if ($hPart -gt 0) { $filenum = "$hPart " } else { $filenum = "" }
        $filebasename = "IVANS BOOKROLL--HOME Combined $filenum($h PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $HomeDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> Home file contains $h policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($b -gt 0) {
        $bPart++
        $BoatDoc = Inject_AgentBookRollInfo -ACORD_XML $BoatDoc -BTCode $BTRecord
        if ($bPart -gt 0) { $filenum = "$bPart " } else { $filenum = "" }
        $filebasename = "IVANS BOOKROLL--BOAT Combined $filenum($b PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $BoatDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> Watercraft file contains $b policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($d -gt 0) {
        $dPart++
        $DwellingDoc = Inject_AgentBookRollInfo -ACORD_XML $DwellingDoc -BTCode $BTRecord
        if ($dPart -gt 0) { $filenum = "$dPart " } else { $filenum = "" }
        $filebasename = "IVANS BOOKROLL--DFIRE Combined $filenum($d PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $DwellingDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> Dwelling file contains $d policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($u -gt 0) {
        $uPart++
        $UmbrellaDoc = Inject_AgentBookRollInfo -ACORD_XML $UmbrellaDoc -BTCode $BTRecord
        if ($uPart -gt 0) { $filenum = "$uPart " } else { $filenum = "" }
        $filebasename = "IVANS BOOKROLL--UMBRP Combined $filenum($u PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $UmbrellaDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> Umbrella file contains $u policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($im -gt 0) {
        $iPart++
        if ($iPart -gt 0) { $filenum = "$iPart " } else { $filenum = "" }
        $filebasename = "MANUAL QUOTE--INMRP Combined $filenum($im PIF BT-$BTRecord).xml"
        $fname = Join-Path -Path $XMLDestinationFolder -ChildPath $filebasename
        $InlandDoc.save($fname)
        Upload_To_AQE_Using_CURL -FolderName $XMLDestinationFolder -FileName $filebasename -BT_Code $BTRecord
        Log -Str "$filebasename --> InlandMarine file contains $im policies." -SkipLog
        Log -Str "$filebasename --> Uploaded to AQE (BT-$BTRecord)." -SkipLog
    }
   if ($unk -gt 0) {
        Log -Str "NOTE: Encountered $unk unknown LOB(s)."
    }
   if ($skip -gt 0) {
        #write-host "Skipped $skip file(s)."
    }
    $total = $aTotal + $a + $hTotal + $h + $uTotal + $u + $bTotal + $b + $dTotal + $d + $iTotal + $im
    Log -Str "Combined policy count: $total"
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Extract_Home_From_Package ($XMLRawText) {
 # HOME

 # INPUT:
# InsuranceSvcRs                  [rename to InsuranceSvcRq]
#  RqUID
#  PolicySyncRs                   [ignore]
#   RqUID
#   PersPkgPolicy                 [rename to HomePolicyQuoteInqRq, move under InsuranceSvcRq]
#    Producer                     [no change]
#    InsuredOrPrincipal *         [no change]
#    PersPolicy                   [no change]
#    Location *                   [no change]
#    Dwell                        [move under HomeLineBusiness]
#    PersDriver *                 [ignore]
#    PersVeh *                    [ignore]
#    PropertySchedule *           [move under HomeLineBusiness]
#    PersPkgHomeLineBusiness      [rename to HomeLineBusiness]
#    PersPkgAutoLineBusiness      [ignore]
#    PersPkgUmbrellaLineBusiness  [ignore]
#    StatRecord *

# HOME
# OUTPUT:
# InsuranceSvcRq
#  HomePolicyQuoteInqRq
#   RqUID
#   CurCd
#   Producer
#   InsuredOrPrincipal *
#   PersPolicy
#   HomeLineBusiness
#    Dwell
#    PropertySchedule *
#   Location
#   RemarkText   

    # Rename key nodes and LOB code
    $NewHomeXML = [xml]$XMLRawText.Replace("PersPkgPolicyQuoteInqRq","HomePolicyQuoteInqRq").Replace("PersPkgHomeLineBusiness","HomeLineBusiness").Replace("<LOBCd>PPKGE</LOBCd>","<LOBCd>HOME</LOBCd>")
    $HomePolicyQuoteInqRqNode = $NewHomeXML.SelectNodes("//HomePolicyQuoteInqRq")
    Add_Metadata -XMLDocument $NewHomeXML -NodeName 'Source_Name' -Value 'IVANS Bookroll'
    Add_Metadata -XMLDocument $NewHomeXML -NodeName 'Notice' -Value 'This policy was derived from a Packaged Policy.'

    $PersPolicyNode = $NewHomeXML.SelectNodes("//PersPolicy")
    $HomeLineBusinessNode = $NewHomeXML.SelectSingleNode("//HomeLineBusiness")

    # Get all of the Refs for HomeLineBusiness
    $DwellRefs = ($HomeLineBusinessNode.DwellRefs).Trim()

    # Copy these over to HomeLineBusiness before they're removed from HomePolicyQuoteInqRq
    # note the space, so we need to trim: DwellRefs="DW0001 "
    $DwellLocationRefs = ""
    $Dwellings = $HomePolicyQuoteInqRqNode.SelectNodes('Dwell')
    Foreach ($dwell in $Dwellings) {
        if (($DwellRefs.IndexOf($dwell.id)) -ge 0) { 
            [void] $HomeLineBusinessNode.AppendChild($NewHomeXML.ImportNode($dwell, $true)) 
            $DwellLocationRefs = $DwellLocationRefs + " " + $dwell.LocationRef # id="DW0001" LocationRef="L0001"
            }
        }

    # remove all Locations except the one referenced by Dwell
    $Locations = $HomePolicyQuoteInqRqNode.SelectNodes('Location')
    Foreach ($loc in $Locations) { if (($DwellLocationRefs.IndexOf($loc.id)) -le 0) { [void] $HomePolicyQuoteInqRqNode.RemoveChild($loc) } }

    $ScheduledProperties = $HomePolicyQuoteInqRqNode.SelectNodes('PropertySchedule')
    Foreach ($prop in $ScheduledProperties) { [void] $HomeLineBusinessNode.AppendChild($NewHomeXML.ImportNode($prop, $true)) }

    # Check to see if there is an InlandMarine LOB, and if so, grab its premium
    $InlandMarinePremium = [int] $HomePolicyQuoteInqRqNode.SelectSingleNode("PersPkgPersInlandMarineLineBusiness/CurrentTermAmt/Amt").InnerXML
    
    # remove unneeded nodes under HomePolicyQuoteInqRqNode
    $NodeRemovals = @('PersDriver','PersVeh','Watercraft','WatercraftAccessory','PersPkgAutoLineBusiness','PersPkgUmbrellaLineBusiness','PersPkgWatercraftLineBusiness','PersPkgPersInlandMarineLineBusiness','Dwell','PropertySchedule')
    Foreach ($node in $NodeRemovals) { $HomePolicyQuoteInqRqNode.SelectNodes("$node") | ForEach-Object { [void] $HomePolicyQuoteInqRqNode.RemoveChild($_) } }

    # remove unneeded nodes under PersPolicyNode
    $NodeRemovals = @('QuestionAnswer','DriverVeh')
    Foreach ($node in $NodeRemovals) { $PersPolicyNode.SelectNodes("$node") | ForEach-Object { [void] $PersPolicyNode.RemoveChild($_) } }

   
    $PolicyPremium = [int] $HomeLineBusinessNode.SelectSingleNode("./CurrentTermAmt/Amt").InnerXML
    if ($PolicyPremium -le 0) {
        # Now recalculate and update the policy premium
        $CalculatedPolicyPremium = 0
        $Coverages = $HomeLineBusinessNode.Dwell.SelectNodes('//Coverage')
        if ($Coverages -ne $null) {
            Foreach ($cov in $Coverages) {
                $covPrem = [int] ($cov.SelectSingleNode("./CurrentTermAmt/Amt")).InnerXML
                $CalculatedPolicyPremium += $covPrem
                }
            }
    # Update the Policy Premium based on the sum of the individual Coverages
        if ($CalculatedPolicyPremium -gt 0) {
            $PolicyPremiumText = [string]::Format("{0:000}", $CalculatedPolicyPremium)
            $CurrentPolicyPremiumAmtNode = $NewHomeXML.SelectSingleNode('//PersPolicy/CurrentTermAmt/Amt')
            $CurrentPolicyPremiumAmtNode.InnerText = $PolicyPremiumText
            # Add a Remark about the derived premium value
            Add_Metadata -XMLDocument $NewHomeXML -NodeName 'Notice' -Value 'This was derived from a Packaged Policy so the policy premium has been calculated from the sum of the coverages.'
            }
        }
    else {
        # Add the InlandMarine premium with the Home premium
        $PersPolicyNode.CurrentTermAmt.Amt = [string]::Format("{0:000}", $PolicyPremium + $InlandMarinePremium)
        }
    
    if ($InlandMarinePremium -gt 0) {
        Add_Metadata -XMLDocument $NewHomeXML -NodeName 'Notice' -Value "This Home policy came from a Packaged Policy in which the Inland Marine was incorporated into the Home policy. Total policy premium is sum of Home premium ($PolicyPremium) and Inland Marine premium ($InlandMarinePremium)."
        }

    return $NewHomeXML
}
######################################################################################################################
######################################################################################################################
Function Extract_InlandMarine_From_Package ($XMLRawText) {
 # HOME

 # INPUT:
# InsuranceSvcRs                  [rename to InsuranceSvcRq]
#  RqUID
#  PolicySyncRs                   [ignore]
#   RqUID
#   PersPkgPolicy                 [rename to PersInlandMarinePolicyQuoteInqRq, move under InsuranceSvcRq]
#    Producer                     [no change]
#    InsuredOrPrincipal *         [no change]
#    PersPolicy                   [no change]
#    Location *                   [no change]
#    Dwell                        [remove]
#    PersDriver *                 [remove]
#    PersVeh *                    [remove]
#    PropertySchedule *           [keep]
#    PersPkgHomeLineBusiness      [remove]
#    PersPkgAutoLineBusiness      [remove]
#    PersPkgUmbrellaLineBusiness  [remove]
#    PersPkgPersInlandMarineLineBusiness PropScheduleRefs="S01 S01 "   [rename to PersInlandMarineLineBusiness]
#    StatRecord *                 [remove]

# PUMBR
# OUTPUT:
# InsuranceSvcRq
#  PersInlandMarinePolicyQuoteInqRq
#   RqUID
#   CurCd
#   Producer
#   InsuredOrPrincipal *
#   PersPolicy
#   Location (include the Location with id that matches those mentioned in PropertySchedule
#   PersInlandMarineLineBusiness (has PropScheduleRefs)
#        Location
#        PropertySchedule (will match ids in PersPkgPersInlandMarineLineBusiness and match LocRefs in Location)
#   RemarkText

#Example: <PersPkgPersInlandMarineLineBusiness PropScheduleRefs="S01 S01 ">
#           <PropertySchedule id="S01" LocationRef="L0003">
#           <PropertySchedule id="S01" LocationRef="L0003">
#           <Location id="L0003">
# get PropScheduleRefs from LineBusiness, use those to get PropertySchedule nodes, use those to get LocationRefs
# Don't forget to split the reference strings and trim them

    # Rename key nodes and LOB code
    $XMLRawText = $XMLRawText.Replace("PersPkgPolicyQuoteInqRq","PersInlandMarinePolicyQuoteInqRq").Replace("PersPkgPersInlandMarineLineBusiness","PersInlandMarineLineBusiness")
    $XMLRawText = $XMLRawText.Replace("<LOBCd>PPKGE</LOBCd>","<LOBCd>INMRP</LOBCd>")
    $NewInlandMarineXML = [xml] $XMLRawText
    $InlandMarinePolicyQuoteInqRqNode = $NewInlandMarineXML.SelectNodes("//PersInlandMarinePolicyQuoteInqRq")
    Add_Metadata -XMLDocument $NewInlandMarineXML -NodeName 'Source_Name' -Value 'IVANS Bookroll'
    Add_Metadata -XMLDocument $NewInlandMarineXML -NodeName 'Notice' -Value 'This policy was derived from a Packaged Policy.' 

    $PersPolicyNode = $NewInlandMarineXML.SelectNodes("//PersPolicy")
    $PersInlandMarineLineBusinessNode = $NewInlandMarineXML.SelectSingleNode("//PersInlandMarineLineBusiness")

    if ($PersPolicyNode.OtherOrPriorPolicy) {
        $CommlName = $PersPolicyNode.OtherOrPriorPolicy.NameInfo.CommercialName.CommlName
        if ($CommlName) {
            $InsurerNameNode = $OtherOrPriorPolicyNode.InsurerName
            if ($InsurerNameNode -eq $null) {
                $InsurerNameNode = ($PersPolicyNode.OtherOrPriorPolicy).AppendChild($NewInlandMarineXML.CreateElement("InsurerName"))
                [void] $InsurerNameNode.AppendChild($NewInlandMarineXML.CreateTextNode($CommlName))
                }
            }
        }

#Example: <PersPkgPersInlandMarineLineBusiness PropScheduleRefs="S01 S01 ">
#           <PropertySchedule id="S01" LocationRef="L0003">
#           <PropertySchedule id="S01" LocationRef="L0003">
#           <Location id="L0003">
# get PropScheduleRefs from LineBusiness, use those to get PropertySchedule nodes, use those to get LocationRefs
    $AllLocationRefs = New-Object -TypeName System.Collections.ArrayList
    $AllPropScheduleRefs = ($PersInlandMarineLineBusinessNode.PropScheduleRefs).Trim().Split()
    $PropSchedules = $InlandMarinePolicyQuoteInqRqNode.SelectNodes('PropertySchedule')
    Foreach ($prop in $PropSchedules) {
        if ($AllPropScheduleRefs.Contains($prop.id) -or $AllPropScheduleRefs.Contains($prop.PropSummaryRef)) {
            if (-not $AllLocationRefs.Contains($prop.LocationRef)) { [void] $AllLocationRefs.Add($prop.LocationRef) }
            [void] $PersInlandMarineLineBusinessNode.AppendChild($NewInlandMarineXML.ImportNode($prop, $true)) 
            }
        # remove the PropSchedule whether referenced or not
        [void] $prop.ParentNode.RemoveChild($prop)
        }

    # Now remove Locations that were not referenced in PropertySchedules
    $Locations = $InlandMarinePolicyQuoteInqRqNode.SelectNodes('Location')
    Foreach ($loc in $Locations) {
        if (-not $AllLocationRefs.Contains($loc.id)) { [void] $loc.ParentNode.RemoveChild($loc) }
        }


    # remove unneeded nodes under InlandMarinePolicyQuoteInqRqNode
    $NodeRemovals = @('PersDriver','PersVeh','Watercraft','PersPkgAutoLineBusiness','PersPkgHomeLineBusiness','PersPkgWatercraftLineBusiness','PropertySchedule')
    Foreach ($node in $NodeRemovals) { $InlandMarinePolicyQuoteInqRqNode.SelectNodes("$node") | ForEach-Object { [void] $InlandMarinePolicyQuoteInqRqNode.RemoveChild($_) } }

    # remove unneeded nodes under PersPolicyNode
    $NodeRemovals = @('QuestionAnswer','Loss','DriverVeh')
    Foreach ($node in $NodeRemovals) { $PersPolicyNode.SelectNodes("$node") | ForEach-Object { [void] $PersPolicyNode.RemoveChild($_) } }


    $PolicyPremium = $PersInlandMarineLineBusinessNode.SelectSingleNode("./CurrentTermAmt/Amt").InnerXML
    if ($PolicyPremium -eq $null) {
        # Now recalculate and update the policy premium
        $CalculatedPolicyPremium = 0
        $Coverages = $PersInlandMarineLineBusinessNode.SelectNodes('Coverage')
        Foreach ($cov in $Coverages) {
            $covPrem = [int] ($cov.SelectSingleNode("./CurrentTermAmt/Amt")).InnerXML
            $CalculatedPolicyPremium += $covPrem
            }
        # Update the Policy Premium based on the sum of the individual Coverages
        if ($CalculatedPolicyPremium -gt 0) {
            $PolicyPremiumText = [string]::Format("{0:000}", $CalculatedPolicyPremium)
            $CurrentPolicyPremiumAmtNode = $NewInlandMarineXML.SelectSingleNode('//PersPolicy/CurrentTermAmt/Amt')
            $CurrentPolicyPremiumAmtNode.InnerText = $PolicyPremiumText
            # Add a Remark about the derived premium value
            Add_Metadata -XMLDocument $NewInlandMarineXML -NodeName 'Notice' -Value 'This was derived from a Packaged Policy so the policy premium has been calculated from the sum of the coverages.'
            }
    }
    else {
        $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremium
        }

    return $NewInlandMarineXML
}
######################################################################################################################
######################################################################################################################
Function Extract_Umbrella_From_Package ($XMLRawText) {
 # HOME

 # INPUT:
# InsuranceSvcRs                  [rename to InsuranceSvcRq]
#  RqUID
#  PolicySyncRs                   [ignore]
#   RqUID
#   PersPkgPolicy                 [rename to PersUmbrellaPolicyQuoteInqRq, move under InsuranceSvcRq]
#    Producer                     [no change]
#    InsuredOrPrincipal *         [no change]
#    PersPolicy                   [no change]
#    Location *                   [no change]
#    Dwell                        [remove]
#    PersDriver *                 [remove]
#    PersVeh *                    [remove]
#    PropertySchedule *           [remove]
#    PersPkgHomeLineBusiness      [remove]
#    PersPkgAutoLineBusiness      [remove]
#    PersPkgUmbrellaLineBusiness  [rename to PersUmbrellaLineBusiness]
#    StatRecord *                 [remove]

# PUMBR
# OUTPUT:
# InsuranceSvcRq
#  PersUmbrellaPolicyQuoteInqRq
#   RqUID
#   CurCd
#   Producer
#   InsuredOrPrincipal *
#   PersPolicy
#   PersUmbrellaLineBusiness (has DwellRefs, LocationRefs, VehicleRefs)
#      Dwell (has LocationRef)
#      PersDriver (include all)
#      PersVeh (must match VehicleRefs in PersUmbrellaLineBusiness)
#   Location (must match Dwell referred to by PersUmbrellaLineBusiness)
#   RemarkText

#Example: <PersPkgUmbrellaLineBusiness DwellRefs="DW0002 DW0003 " VehRefs="V0001 V0002 " WatercraftRefs="W001 ">
# Don't forget to split the reference strings and trim them

    # Rename key nodes and LOB code
    $XMLRawText = $XMLRawText.Replace("PersPkgPolicyQuoteInqRq","PersUmbrellaPolicyQuoteInqRq").Replace("PersPkgUmbrellaLineBusiness","PersUmbrellaLineBusiness")
    $XMLRawText = $XMLRawText.Replace("<LOBCd>PPKGE</LOBCd>","<LOBCd>UMBRP</LOBCd>").Replace("<LOBCd>PUMBR</LOBCd>","<LOBCd>UMBRP</LOBCd>")
    $XMLRawText = $XMLRawText.Replace("<PersVeh ","<BasicVehInfo ").Replace("</PersVeh>","</BasicVehInfo>")
    $XMLRawText = $XMLRawText.Replace("<PersDriver ","<BasicDriverInfo ").Replace("</PersDriver>","</BasicDriverInfo>")
    $XMLRawText = $XMLRawText.Replace("<Watercraft ","<BasicWatercraftInfo ").Replace("</Watercraft>","</BasicWatercraftInfo>")
    $NewUmbrellaXML = [xml] $XMLRawText
    $UmbrellaPolicyQuoteInqRqNode = $NewUmbrellaXML.SelectNodes("//PersUmbrellaPolicyQuoteInqRq")
    Add_Metadata -XMLDocument $NewUmbrellaXML -NodeName 'Source_Name' -Value 'IVANS Bookroll'
    Add_Metadata -XMLDocument $NewUmbrellaXML -NodeName 'Notice' -Value 'This policy was derived from a Packaged Policy.' 

    $PersPolicyNode = $NewUmbrellaXML.SelectNodes("//PersPolicy")
    $PersUmbrellaLineBusinessNode = $NewUmbrellaXML.SelectSingleNode("//PersUmbrellaLineBusiness")
    
    if ($PersPolicyNode.OtherOrPriorPolicy) {
        $CommlName = $PersPolicyNode.OtherOrPriorPolicy.NameInfo.CommercialName.CommlName
        if ($CommlName) {
            $InsurerNameNode = $OtherOrPriorPolicyNode.InsurerName
            if ($InsurerNameNode -eq $null) {
                $InsurerNameNode = ($PersPolicyNode.OtherOrPriorPolicy).AppendChild($NewUmbrellaXML.CreateElement("InsurerName"))
                [void] $InsurerNameNode.AppendChild($NewUmbrellaXML.CreateTextNode($CommlName))
                }
            }
        }

    # Get the LocationRefs from the Dwells
    # note the space, so we need to trim: <PersPkgUmbrellaLineBusiness DwellRefs="DW0002 DW0003 " VehRefs="V0001 V0002 " WatercraftRefs="W001 ">
    $AllLocationRefs = New-Object -TypeName System.Collections.ArrayList
    if ($PersUmbrellaLineBusinessNode.DwellRefs -ne $null) {
        $AllDwellRefs = ($PersUmbrellaLineBusinessNode.DwellRefs).Trim().Split()
        $Dwellings = $UmbrellaPolicyQuoteInqRqNode.SelectNodes('Dwell')
        Foreach ($dwell in $Dwellings) {
            Foreach ($ref in $AllDwellRefs) {
                if ($ref -eq ($dwell.id)) { 
     #               [void] $PersUmbrellaLineBusinessNode.AppendChild($NewUmbrellaXML.ImportNode($dwell, $true)) 
                    if ( -not ($AllLocationRefs.Contains($dwell.LocationRef))) { [void] $AllLocationRefs.Add($dwell.LocationRef) } # id="DW0001" LocationRef="L0001"
                    }
                }
            [void] $dwell.ParentNode.RemoveChild($dwell)
            }
        }

    # move all WaterCrafts to be under PersUmbrellaLineBusiness
    if ($PersUmbrellaLineBusinessNode.WatercraftRefs -ne $null) {
        $AllWatercraftRefs = ($PersUmbrellaLineBusinessNode.WatercraftRefs).Trim().Split()
        $Watercrafts = $UmbrellaPolicyQuoteInqRqNode.SelectNodes('BasicWatercraftInfo')
        Foreach ($boat in $Watercrafts) {
            Foreach ($ref in $AllWatercraftRefs) {
                if ($ref -eq ($boat.id)) { 
                    [void] $PersUmbrellaLineBusinessNode.AppendChild($NewUmbrellaXML.ImportNode($boat, $true))
                    if ( -not ($AllLocationRefs.Contains($boat.LocationRef))) { [void] $AllLocationRefs.Add($boat.LocationRef) } # <Watercraft id="W001" LocationRef="L0001">
                    }    
                }        
            [void] $boat.ParentNode.RemoveChild($boat)
            }
        }

    # move all Vehicles to be under PersUmbrellaLineBusiness
    if ($PersUmbrellaLineBusinessNode.VehRefs -ne $null) {
    $AllVehRefs = ($PersUmbrellaLineBusinessNode.VehRefs).Trim().Split()
        $PersVehs = $UmbrellaPolicyQuoteInqRqNode.SelectNodes('BasicVehInfo')
        Foreach ($veh in $PersVehs) {
            Foreach ($ref in $AllVehRefs) {
                if ($ref -eq ($veh.id)) { 
                    [void] $PersUmbrellaLineBusinessNode.AppendChild($NewUmbrellaXML.ImportNode($veh, $true)) 
                    if ( -not ($AllLocationRefs.Contains($veh.LocationRef))) { [void] $AllLocationRefs.Add($veh.LocationRef) } # <PersVeh id="V0001" LocationRef="L0001">
                    }
                }
            [void] $veh.ParentNode.RemoveChild($veh)
            }
        }
    # move over all the Drivers    DwellRefs="DW0002 " DriverRefs="D0001 " VehRefs="V0001 V0002 " WatercraftRefs="W001 "
    # to mover over only the ones referenced, remove comment hashtags
#    if ($PersUmbrellaLineBusinessNode.DriverRefs -ne $null) {
#        $AllDriverRefs = ($PersUmbrellaLineBusinessNode.DriverRefs).Trim().Split()
        $Drivers = $UmbrellaPolicyQuoteInqRqNode.SelectNodes('BasicDriverInfo')
        Foreach ($drv in $Drivers) {
#            Foreach ($ref in $AllDriverRefs) {
#                if ($ref -eq ($drv.id)) { 
                    [void] $PersUmbrellaLineBusinessNode.AppendChild($NewUmbrellaXML.ImportNode($drv, $true))
#                    }
#                }
            [void] $drv.ParentNode.RemoveChild($drv)
#            }
       }

    # remove all Locations except the one(s) referenced by Dwell, Vehicle, Watercraft
    $Locations = $UmbrellaPolicyQuoteInqRqNode.SelectNodes('Location')
    Foreach ($loc in $Locations) { if (-not $AllLocationRefs.Contains($loc.id)) { Try { [void] $UmbrellaPolicyQuoteInqRqNode.RemoveChild($loc) } Catch { } } }

    # remove unneeded nodes under UmbrellaPolicyQuoteInqRqNode
    $NodeRemovals = @('PersDriver','PersVeh','Watercraft','PersPkgAutoLineBusiness','PersPkgHomeLineBusiness','PersPkgWatercraftLineBusiness','PropertySchedule')
    Foreach ($node in $NodeRemovals) { $UmbrellaPolicyQuoteInqRqNode.SelectNodes("$node") | ForEach-Object { Try { [void] $UmbrellaPolicyQuoteInqRqNode.RemoveChild($_) } Catch { } } }

    # remove unneeded nodes under PersPolicyNode
    $NodeRemovals = @('QuestionAnswer','Loss','DriverVeh')
    Foreach ($node in $NodeRemovals) { $PersPolicyNode.SelectNodes("$node") | ForEach-Object { Try { [void] $PersPolicyNode.RemoveChild($_) } Catch { } } }

    # remove Coverages from Dwell (these are Home and Auto coverages)
    ($UmbrellaPolicyQuoteInqRqNode.Dwell).SelectNodes("Coverage") | ForEach-Object { Try { [void] ($UmbrellaPolicyQuoteInqRqNode.Dwell).RemoveChild($_) } Catch { } } 

   
    $PolicyPremium = $PersUmbrellaLineBusinessNode.SelectSingleNode("./CurrentTermAmt/Amt").InnerXML
    if ($PolicyPremium -eq $null) {
        # Now recalculate and update the policy premium
        $CalculatedPolicyPremium = 0
        $Coverages = $PersUmbrellaLineBusinessNode.SelectNodes('Coverage')
        Foreach ($cov in $Coverages) {
            $covPrem = [int] ($cov.SelectSingleNode("./CurrentTermAmt/Amt")).InnerXML
            $CalculatedPolicyPremium += $covPrem
            }
        # Update the Policy Premium based on the sum of the individual Coverages
        if ($CalculatedPolicyPremium -gt 0) {
            $PolicyPremiumText = [string]::Format("{0:000}", $CalculatedPolicyPremium)
            $CurrentPolicyPremiumAmtNode = $NewUmbrellaXML.SelectSingleNode('//PersPolicy/CurrentTermAmt/Amt')
            $CurrentPolicyPremiumAmtNode.InnerText = $PolicyPremiumText
            # Add a Remark about the derived premium value
            Add_Metadata -XMLDocument $NewUmbrellaXML -NodeName 'Notice' -Value 'This was derived from a Packaged Policy so the policy premium has been calculated from the sum of the coverages.'
            }
    }
    else {
        $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremium
        }

    return $NewUmbrellaXML
}
######################################################################################################################
######################################################################################################################
Function Extract_Watercraft_From_Package ($XMLRawText) {
 # HOME

 # INPUT:
# InsuranceSvcRs                  [rename to InsuranceSvcRq]
#  RqUID
#  PolicySyncRs                   [ignore]
#   RqUID
#   PersPkgPolicy                 [rename to PersUmbrellaPolicyQuoteInqRq, move under InsuranceSvcRq]
#    Producer                     [no change]
#    InsuredOrPrincipal *         [no change]
#    PersPolicy                   [no change]
#    Location *                   [no change]
#    Dwell                        [remove]
#    PersDriver *                 [remove]
#    PersVeh *                    [remove]
#    PropertySchedule *           [remove]
#    PersPkgHomeLineBusiness      [remove]
#    PersPkgAutoLineBusiness      [remove]
#    PersPkgUmbrellaLineBusiness  [rename to PersUmbrellaLineBusiness]
#    StatRecord *                 [remove]

# PUMBR
# OUTPUT:
# InsuranceSvcRq
#  WatercraftPolicyQuoteInqRq
#   RqUID
#   CurCd
#   Producer
#   InsuredOrPrincipal *
#   PersPolicy
#   WatercraftLineBusiness (has DwellRefs, LocationRefs, VehicleRefs)
#      Watercraft (has LocationRef)
#      WatercraftAccessory
#      PersDriver (include all)
#   Location (must match Dwell referred to by PersUmbrellaLineBusiness)
#   RemarkText

#Example: <PersPkgUmbrellaLineBusiness DwellRefs="DW0002 DW0003 " VehRefs="V0001 V0002 " WatercraftRefs="W001 ">
# Don't forget to split the reference strings and trim them

    # Rename key nodes and LOB code
    $XMLRawText = $XMLRawText.Replace("PersPkgPolicyQuoteInqRq","WatercraftPolicyQuoteInqRq").Replace("PersPkgWatercraftLineBusiness","WatercraftLineBusiness")
    $XMLRawText = $XMLRawText.Replace("<LOBCd>PPKGE</LOBCd>","<LOBCd>BOAT</LOBCd>")
    $NewWatercraftXML = [xml] $XMLRawText
    $WatercraftPolicyQuoteInqRqNode = $NewWatercraftXML.SelectNodes("//WatercraftPolicyQuoteInqRq")
    Add_Metadata -XMLDocument $NewWatercraftXML -NodeName 'Source_Name' -Value 'IVANS Bookroll'
    Add_Metadata -XMLDocument $NewWatercraftXML -NodeName 'Notice' -Value 'This policy was derived from a Packaged Policy.' 

    $PersPolicyNode = $NewWatercraftXML.SelectNodes("//PersPolicy")
    $WatercraftLineBusinessNode = $NewWatercraftXML.SelectSingleNode("//WatercraftLineBusiness")

    $AllLocationRefs = New-Object -TypeName System.Collections.ArrayList

    # move all WaterCrafts to be under WatercraftLineBusiness
    $Watercrafts = $WatercraftPolicyQuoteInqRqNode.SelectNodes('Watercraft')
    Foreach ($boat in $Watercrafts) {
        [void] $WatercraftLineBusinessNode.AppendChild($NewWatercraftXML.ImportNode($boat, $true))
        [void] $WatercraftPolicyQuoteInqRqNode.RemoveChild($boat)
        if ( -not ($AllLocationRefs.Contains($boat.LocationRef))) { [void] $AllLocationRefs.Add($boat.LocationRef) } # <Watercraft id="W401" LocationRef="L0002">
        }

    # move all WatercraftAccessory to be under WatercraftLineBusiness
    $WatercraftAccessories = $WatercraftPolicyQuoteInqRqNode.SelectNodes('WatercraftAccessory')
    Foreach ($wacc in $WatercraftAccessories) {
        [void] $WatercraftLineBusinessNode.AppendChild($NewWatercraftXML.ImportNode($wacc, $true)) 
        [void] $WatercraftPolicyQuoteInqRqNode.RemoveChild($wacc)
        if ( -not ($AllLocationRefs.Contains($wacc.LocationRef))) { [void] $AllLocationRefs.Add($wacc.LocationRef) } # <WatercraftAccessory id="WA003" WatercraftRef="W401" LocationRef="L0002">
      }

    # remove all Locations except the one(s) referenced PersVehs
    $Locations = $WatercraftPolicyQuoteInqRqNode.SelectNodes('Location')
    Foreach ($loc in $Locations) { if (-not $AllLocationRefs.Contains($loc.id)) { [void] $WatercraftPolicyQuoteInqRqNode.RemoveChild($loc) } }

    # move all Drivers to be under WatercraftLineBusiness
    $Drivers = $WatercraftPolicyQuoteInqRqNode.SelectNodes('PersDriver')
    Foreach ($drv in $Drivers) {
        [void] $WatercraftLineBusinessNode.AppendChild($NewWatercraftXML.ImportNode($drv, $true)) 
        [void] $WatercraftPolicyQuoteInqRqNode.RemoveChild($drv)
        }

    # remove unneeded nodes under WatercraftPolicyQuoteInqRqNode
    $NodeRemovals = @('PersVeh','Dwell','PersPkgAutoLineBusiness','PersPkgHomeLineBusiness','PropertySchedule')
    Foreach ($node in $NodeRemovals) { $WatercraftPolicyQuoteInqRqNode.SelectNodes("$node") | ForEach-Object { [void] $WatercraftPolicyQuoteInqRqNode.RemoveChild($_) } }

    # remove unneeded nodes under PersPolicyNode
    $NodeRemovals = @('QuestionAnswer','Loss','DriverVeh')
    Foreach ($node in $NodeRemovals) { $PersPolicyNode.SelectNodes("$node") | ForEach-Object { [void] $PersPolicyNode.RemoveChild($_) } }



    $PolicyPremium = $WatercraftLineBusinessNode.SelectSingleNode("./CurrentTermAmt/Amt").InnerXML
    if ($PolicyPremium -eq $null) {
        # Calculate Watercraft and WatercraftAccessory premiums and update PolicyPremium
        $CalculatedPolicyPremium = 0
        $Watercrafts = $WatercraftLineBusinessNode.SelectNodes('Watercraft')
        Foreach ($boat in $Watercrafts) {
            # calculate per watercraft premium and inject new node
            $boatPrem = ($boat.SelectSingleNode("./FullTermAmt/Amt")).InnerXML
            if ( $boatPrem -eq $null ) {
                $boatPrem = 0
                $prem = $boat.SelectNodes('./Coverage/CurrentTermAmt/Amt')
                $prem | Foreach { $boatPrem += [int]$_.InnerText}
                if ($boatPrem -gt 0) {
                    $boatPremText = [string]::Format("{0:000}", $boatPrem)
                    $newFullTermAmtNode = $boat.AppendChild($NewWatercraftXML.CreateElement("CurrentTermAmt"));
                    $newFullTermAmtAmt = $newFullTermAmtNode.AppendChild($NewWatercraftXML.CreateElement("Amt"));
                    $newFullTermAmtAmtNode = $newFullTermAmtAmt.AppendChild($NewWatercraftXML.CreateTextNode($boatPremText));
                    }
                }
            $CalculatedPolicyPremium += $boatPrem
            }
        $WatercraftAccessories = $WatercraftLineBusinessNode.SelectNodes('WatercraftAccessory')
        Foreach ($wacc in $WatercraftAccessories) {
            # calculate per accessory premium and inject new node
            $boatPrem = ($wacc.SelectSingleNode("./FullTermAmt/Amt")).InnerXML
            if ( $boatPrem -eq $null ) {
                $boatPrem = 0
                $prem = $wacc.SelectNodes('./Coverage/CurrentTermAmt/Amt')
                $prem | Foreach { $boatPrem += [int]$_.InnerText}
                if ($boatPrem -gt 0) {
                    $boatPremText = [string]::Format("{0:000}", $boatPrem)
                    $newFullTermAmtNode = $boat.AppendChild($NewWatercraftXML.CreateElement("CurrentTermAmt"));
                    $newFullTermAmtAmt = $newFullTermAmtNode.AppendChild($NewWatercraftXML.CreateElement("Amt"));
                    $newFullTermAmtAmtNode = $newFullTermAmtAmt.AppendChild($NewWatercraftXML.CreateTextNode($boatPremText));
                    }
                }
            $CalculatedPolicyPremium += $boatPrem
            }
        # Update the Policy Premium based on the sum of the individual Coverages
        if ($CalculatedPolicyPremium -gt 0) {
            $PolicyPremiumText = [string]::Format("{0:000}", $CalculatedPolicyPremium)
            $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremiumText
            # Add a Remark about the derived premium value
            Add_Metadata -XMLDocument $NewWatercraftXML -NodeName 'Notice' -Value 'This was derived from a Packaged Policy so the policy premium has been calculated from the sum of the coverages.'
            }
        }
    else {
        $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremium
        }

    return $NewWatercraftXML
}
######################################################################################################################
######################################################################################################################
Function Extract_Auto_From_Package ($XMLRawText) {
# INPUT:
# InsuranceSvcRs                  [rename to InsuranceSvcRq]
#  RqUID
#  PolicySyncRs                   [ignore]
#   RqUID
#   PersPkgPolicy                 [rename to PersAutoPolicyQuoteInqRq, move under InsuranceSvcRq]
#    Producer
#    InsuredOrPrincipal *
#    PersPolicy
#    Location *
#    Dwell
#    PersDriver *                 [move under PersAutoLineBusiness]
#    PersVeh *                    [move under PersAutoLineBusiness]
#    PropertySchedule *           [ignore]
#    PersPkgHomeLineBusiness      [ignore]
#    PersPkgAutoLineBusiness      [rename to PersAutoLineBusiness]
#    PersPkgUmbrellaLineBusiness  [ignore]
#    StatRecord *

# AUTOP
# OUTPUT:
# InsuranceSvcRq
#  PersAutoPolicyQuoteInqRq
#   RqUID
#   CurCd
#   Producer
#   InsuredOrPrincipal *
#   PersPolicy
#   PersAutoLineBusiness
#    PersDriver *
#    PersVeh *
#   Location
#   RemarkText   

#Example: <PersPkgUmbrellaLineBusiness DwellRefs="DW0002 DW0003 " VehRefs="V0001 V0002 " WatercraftRefs="W001 ">
# Don't forget to split the reference strings and trim them

    # Rename key nodes and LOB code
    $XMLRawText = $XMLRawText.Replace("PersPkgPolicyQuoteInqRq","PersAutoPolicyQuoteInqRq").Replace("PersPkgAutoLineBusiness","PersAutoLineBusiness")
    $XMLRawText = $XMLRawText.Replace("<LOBCd>PPKGE</LOBCd>","<LOBCd>AUTOP</LOBCd>")
    $NewAutoXML = [xml] $XMLRawText
    $PersAutoPolicyQuoteInqRqNode = $NewAutoXML.SelectNodes("//PersAutoPolicyQuoteInqRq")
    Add_Metadata -XMLDocument $NewAutoXML -NodeName 'Source_Name' -Value 'IVANS Bookroll'
    Add_Metadata -XMLDocument $NewAutoXML -NodeName 'Notice' -Value 'This policy was derived from a Packaged Policy.' 

    $PersPolicyNode = $NewAutoXML.SelectNodes("//PersPolicy")
    $PersAutoLineBusinessNode = $NewAutoXML.SelectSingleNode("//PersAutoLineBusiness")

    $AllLocationRefs = New-Object -TypeName System.Collections.ArrayList

    # move all PerVehs to be under PersAutoLineBusiness
    $Vehicles = $PersAutoPolicyQuoteInqRqNode.SelectNodes('PersVeh')
    Foreach ($veh in $Vehicles) {
        [void] $PersAutoLineBusinessNode.AppendChild($NewAutoXML.ImportNode($veh, $true))
        [void] $PersAutoPolicyQuoteInqRqNode.RemoveChild($veh)
        if ( -not ($AllLocationRefs.Contains($veh.LocationRef))) { [void] $AllLocationRefs.Add($veh.LocationRef) } # <PersVeh id="V0001" LocationRef="L0001" RatedDriverRef="D0003">
        }

    # remove all Locations except the one(s) referenced PersVehs
    $Locations = $PersAutoPolicyQuoteInqRqNode.SelectNodes('Location')
    Foreach ($loc in $Locations) { if (-not $AllLocationRefs.Contains($loc.id)) { [void] $PersAutoPolicyQuoteInqRqNode.RemoveChild($loc) } }

    # move all Drivers to be under PersAutoLineBusiness
    $Drivers = $PersAutoPolicyQuoteInqRqNode.SelectNodes('PersDriver')
    Foreach ($drv in $Drivers) {
        [void] $PersAutoLineBusinessNode.AppendChild($NewAutoXML.ImportNode($drv, $true)) 
        [void] $PersAutoPolicyQuoteInqRqNode.RemoveChild($drv)
        }

    # remove unneeded nodes under PersAutoPolicyQuoteInqRqNode
    $NodeRemovals = @('Watercraft','WatercraftAccessory','Dwell','PersPkgHomeLineBusiness','PersPkgHomeLineBusiness','PropertySchedule','PersPkgUmbrellaLineBusiness','PersPkgWatercraftLineBusiness','PersPkgPersInlandMarineLineBusiness')
    Foreach ($node in $NodeRemovals) { $PersAutoPolicyQuoteInqRqNode.SelectNodes("$node") | ForEach-Object { [void] $PersAutoPolicyQuoteInqRqNode.RemoveChild($_) } }

    # remove unneeded nodes under PersPolicyNode
    $NodeRemovals = @('QuestionAnswer','Loss','DriverVeh')
    Foreach ($node in $NodeRemovals) { $PersPolicyNode.SelectNodes("$node") | ForEach-Object { [void] $PersPolicyNode.RemoveChild($_) } }

    $PolicyPremium = $PersAutoLineBusinessNode.SelectSingleNode("./CurrentTermAmt/Amt").InnerXML
    if ($PolicyPremium -eq $null) {
        # Calculate per-Vehicle premiums and update PolicyPremium
        $PolicyPremium = 0
        $Vehicles = $NewAutoXML.SelectNodes('//PersVeh')
        Foreach ($vehicle in $Vehicles) {
            # calculate per vehicle premium and inject new node
            $vehPrem = ($vehicle.SelectSingleNode("./FullTermAmt/Amt")).InnerXML
            if ($vehPrem -eq $null) {
                $vehPrem = 0
                #write-host "Calculating per-vehicle premium"
                $prem = $vehicle.SelectNodes('./Coverage/CurrentTermAmt/Amt')
                $prem | Foreach { $vehPrem += [int]$_.InnerText}
                $vehPremText = [string]::Format("{0:000}", $vehPrem)
                $newFullTermAmtNode = $vehicle.AppendChild($NewAutoXML.CreateElement("FullTermAmt"));
                $newFullTermAmtAmt = $newFullTermAmtNode.AppendChild($NewAutoXML.CreateElement("Amt"));
                $newFullTermAmtAmtNode = $newFullTermAmtAmt.AppendChild($NewAutoXML.CreateTextNode($vehPremText));
                #write-host "[$PolicyNo] Updated Vehicle Premium to $vehPremText."
                }
            $PolicyPremium += $vehPrem
            }
            # Update the Policy Premium based on the sum of PersVeh premiums
        if ($PolicyPremium -gt 0) {
            $PolicyPremiumText = [string]::Format("{0:000}", $PolicyPremium)
            $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremiumText
            #write-host "[$PolicyNo] Updated Policy Premium to $PolicyPremiumText."
            # Add a Remark about the derived premium value
            Add_Metadata -XMLDocument $NewAutoXML -NodeName 'Notice' -Value 'This was derived from a Packaged Policy so the policy premium has been calculated from the sum of the coverages.' 
            }
        }
    else {
        $PersPolicyNode.CurrentTermAmt.Amt = $PolicyPremium
        }

    return $NewAutoXML
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Split_Package_XML_File () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XmlDocument,
        [Parameter(Mandatory=$true)]
        $XMLFolder,   
        [Parameter(Mandatory=$true)]
        $DestinationFolder
        )

    $HomePolicyXML = $null; $AutoPolicyXML = $null; $WatercraftPolicyXML = $null; $UmbrellaPolicyXML = $null; $DwellingFirePolicyXML = $null; $InlandMarinePolicyXML = $null;

#    Log -Str "Package Policy found, BEGIN splitting Package Policy into individual LOB files..." 

    $global:Counts['PackagedPolicies']++

    $RawXMLText = $XmlDocument.OuterXML

    if (($XmlDocument.SelectSingleNode("//PersPkgAutoLineBusiness")) -ne $null) {
        $AutoPolicyXML = Extract_Auto_From_Package $RawXMLText
#        Log -Str "Created AUTOP policy from Package Policy."
    } 
    if (($XmlDocument.SelectSingleNode("//PersPkgWatercraftLineBusiness")) -ne $null) {
        $WatercraftPolicyXML = Extract_Watercraft_From_Package $RawXMLText
#        Log -Str "Created BOAT policy from Package Policy."
    }
    if (($XmlDocument.SelectSingleNode("//PersPkgUmbrellaLineBusiness")) -ne $null) {
        $UmbrellaPolicyXML = Extract_Umbrella_From_Package $RawXMLText
#        Log -Str "Created UMBRP policy from Package Policy."
    }   
    if (($XmlDocument.SelectSingleNode("//PersPkgHomeLineBusiness")) -ne $null) {
        $HomePolicyXML = Extract_Home_From_Package $RawXMLText
#        Log -Str "Created HOME policy from Package Policy."
    }
<# we are not splitting out InlandMarine because these will be folded in with the accompanying Home policy
    if (($XmlDocument.SelectSingleNode("//PersPkgPersInlandMarineLineBusiness")) -ne $null) {
        $InlandMarinePolicyXML = Extract_InlandMarine_From_Package $RawXMLText
    }
#>

    $NewDocs = @($HomePolicyXML,$AutoPolicyXML,$UmbrellaPolicyXML,$WatercraftPolicyXML,$DwellingFirePolicyXML,$InlandMarinePolicyXML)
    write-host '[' -ForegroundColor 'Gray' -NoNewline 
    Foreach ($newDoc in $NewDocs) {
        if ($newDoc -ne $null) {
            $newDoc = Inject_StyleSheet_Link $newDoc
            $newDoc = Check_And_Adjust_Names $newDoc
                
            $newDoc = Inject_CommercialName_Into_ACORD_XML ($newDoc)
            $newName = Filename_From_XML $newDoc
            $newName = Join-Path -Path $DestinationFolder -ChildPath $newName
#                $newName = Unique_Filename (Join-Path -Path $DestinationFolder -ChildPath $newName)
            if ($newName -ne "") { $newDoc.Save($newName) }
            
            switch (LOBCd_From_ACORD_XML ($newDoc)) {
                "AUTOP" { $ch = 'a' }
                "HOME"  { $ch = 'h' }
                "UMBRP" { $ch = 'u' }
                "BOAT"  { $ch = 'b' }
                "DFIRE" { $ch = 'd' }
#                "INMRP" { $ch = 'i' }
                default { $ch = '' }
                }
            write-host $ch -ForegroundColor 'Cyan' -NoNewline
            }
        }
    write-host ']' -ForegroundColor 'Gray' -NoNewline
#    Log -Str "END splitting Package Policy into individual LOB files." -SkipLog
}

######################################################################################################################

######################################################################################################################

#$NameParts=@{ 'Prefix' = 'Dr.'; 'FirstName' = 'Daniel'; 'MiddleName' = 'Conrad'; 'LastName' = 'Benson'; 'Suffix' = 'Sr.' } 
#foreach ($key in $associativeArray.Keys){ $key } 
#foreach ($key in $associativeArray.Keys){ write-host $key ":" $associativeArray[$key] } 


# For names:
#  a. break into parts (TitlePrefix, GivenName, OtherGivenName, Surname, NameSuffix), create nodes, if missing

# 1 instance of InsuredOrPrincipal with 1 person
#    a. if contained only in CommercialName, leave alone, create Name Parts if missing
#    b. if contained in Name Parts, leave alone, create CommercialName if missing
#
# 1 instance of InsuredOrPrincipal with 2 people ( '&' )
#    a. if contained only in CommercialName, leave alone and:
#         i. split into Left (Insured) and Right (CoInsured)
#         j. foreach, create Name Parts
#         k. update Insured with (new) Left Name Parts
#         l. create CoInsured using Right Name Parts
#         
#    b. Adjust
#
# create CommercialName, if missing

######################################################################################################################
Function Check_And_Adjust_Names_NUMBER_2 ($ACORD_XML) {
    $PrimaryInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    $CoInsured = $ACORD_XML.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Coinsured']")

    if ($CoInsured -eq $null) {
    # Check whether 2 people are listed in Primary Insured, if so, split them up
    # and if no CommercialName exists, set it
        $PersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
        $PrimaryInsuredName = $PersonName.TitlePrefix + ' ' + $PersonName.GivenName + ' ' + $PersonName.OtherGivenName + ' ' + $PersonName.SurName + ' ' + $PersonName.NameSuffix + ' '
        $PrimaryInsuredName = $PrimaryInsuredName.Replace('  ',' ').Trim()
        $NewCommercialName = $PrimaryInsuredName
        $ampersand = $PrimaryInsuredName.IndexOf('&')
        if ($ampersand -gt 0) { 
            # we have 2 people in 1 InsuredOrPrincipal so we need to split them up and create a CoInsured
            $SecondHalf = $PrimaryInsuredName.SubString($ampersand + 1, $PrimaryInsuredName.Length - $ampersand - 1).Trim()
            $NameElements = $SecondHalf.Split(' ')
            $SurName = $NameElements[$NameElements.Count - 1]
            $GivenName = $SecondHalf.SubString(0, $SecondHalf.IndexOf($SurName) - 1).Trim()
        
            $CoInsured = ($PrimaryInsured.ParentNode).AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipal'));
            $newInsuredOrPrincipalInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalInfo'));
            $InsuredOrPrincipalRoleCd = $newInsuredOrPrincipalInfo.AppendChild($ACORD_XML.CreateElement('InsuredOrPrincipalRoleCd'));
            [void] $InsuredOrPrincipalRoleCd.AppendChild($ACORD_XML.CreateTextNode('Coinsured'));

            $newGeneralPartyInfo = $CoInsured.AppendChild($ACORD_XML.CreateElement('GeneralPartyInfo'));
            $newNameInfo = $newGeneralPartyInfo.AppendChild($ACORD_XML.CreateElement('NameInfo'));
            $newPersonName = $newNameInfo.AppendChild($ACORD_XML.CreateElement('PersonName'));
            $newGivenName = $newPersonName.AppendChild($ACORD_XML.CreateElement('GivenName'));
            $newSurname = $newPersonName.AppendChild($ACORD_XML.CreateElement('Surname'));
            [void] $newGivenName.AppendChild($ACORD_XML.CreateTextNode($GivenName));
            [void] $newSurname.AppendChild($ACORD_XML.CreateTextNode($SurName));

            $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName.GivenName = $PrimaryInsuredName.SubString(0,$ampersand - 1).Trim()
            $PersonName = $PrimaryInsured.GeneralPartyInfo.NameInfo.PersonName
            $NewCommercialName = $PersonName.TitlePrefix + ' ' + $PersonName.GivenName + ' ' + $PersonName.OtherGivenName + ' ' + $PersonName.SurName + ' ' + $PersonName.NameSuffix + ' '
            $PersonName = $CoInsured.GeneralPartyInfo.NameInfo.PersonName
            $NewCommercialName = $NewCommercialName + ' & ' + $PersonName.TitlePrefix + ' '  + $PersonName.GivenName + ' ' + $PersonName.OtherGivenName + ' ' + $PersonName.SurName + ' ' + $PersonName.NameSuffix + ' '
            $NewCommercialName = $NewCommercialName.Replace('  ',' ').Trim()
            }

        $CommercialName = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName.CommercialName
        if ($CommercialName -eq $null) {
            $CommlNameNode = $PrimaryInsured.GeneralPartyInfo.NameInfo.CommlName
            if ($CommlNameNode -eq $null) { $CommlNameNode = ($PrimaryInsured.GeneralPartyInfo.NameInfo).AppendChild($ACORD_XML.CreateElement("CommlName")); }
            $CommercialNameNode = $CommlNameNode.CommercialName
            if ($CommercialNameNode -eq $null) { $CommercialNameNode = $CommlNameNode.AppendChild($ACORD_XML.CreateElement("CommercialName")); }
            [void] $CommercialNameNode.AppendChild($ACORD_XML.CreateTextNode($NewCommercialName));
            }
        }
    $ACORD_XML = Distribute_CommercialName $ACORD_XML
    return $ACORD_XML
}
######################################################################################################################
Add-Type -AssemblyName System.IO.Compression.FileSystem
function Unzip () {
    Param([Parameter(Mandatory=$true)] $ZipFile,          
          [Parameter(Mandatory=$true)] $DestinationFolder )
    [System.IO.Compression.ZipFile]::ExtractToDirectory($ZipFile, $DestinationFolder)
}
######################################################################################################################
######################################################################################################################
function Copy_StyleSheets () {
    Param(
        [Parameter(Mandatory=$true)]
        $SourceFolder,
        [Parameter(Mandatory=$true)]
        $DestinationFolder
        )
    Get-ChildItem -Path $SourceFolder -Filter *.xslt -File  | Sort name  | Foreach-Object {
        Copy-Item -Path $_.FullName -Destination $DestinationFolder -Force
        }
}
######################################################################################################################
function Load_HeaderSummary_File () {
    Param([Parameter(Mandatory=$true)] $SourceFolder )
    $HeaderSummaryFile = Join-Path -Path $SourceFolder -ChildPath "HeaderSummary.xml"
    $RawXMLText = Get-content $HeaderSummaryFile -raw
    $RawXMLText = $RawXMLText.Replace(' xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"','')
    return [xml] $RawXMLText
}
######################################################################################################################
######################################################################################################################
$BTRecord_Lookup = @(
@{ 'YAccount' = 'NEW'; 'BTRecord' = 'NEW'; }
)

<#
$BOOKROLL_FOLDER = "C:\Users\<USER>\Downloads\_IVANS Bookroll\Bookrolls"
cd $BOOKROLL_FOLDER
#>

######################################################################################################################
Function Inject_AgentBookRollInfo () {
    Param(
        [Parameter(Mandatory=$true)]
        $ACORD_XML,
        [Parameter(Mandatory=$true)]
        $BTCode
        )

    $CampaignId = $ACORD_XML.ACORD.AgentBookRollInfo.CarrierPortalInfo.CarrierInfo.CampaignId
    if ($CampaignId -eq $null) {
        $AgentBookRollInfoNode = ($ACORD_XML.ACORD).AppendChild($ACORD_XML.CreateElement("AgentBookRollInfo"))
        $CarrierPortalInfoNode = $AgentBookRollInfoNode.AppendChild($ACORD_XML.CreateElement("CarrierPortalInfo"))
        $CarrierInfoNode = $CarrierPortalInfoNode.AppendChild($ACORD_XML.CreateElement("CarrierInfo"))
        $UserNameNode = $CarrierInfoNode.AppendChild($ACORD_XML.CreateElement("UserName")) # this node remains empty
        $CampaignIdNode = $CarrierInfoNode.AppendChild($ACORD_XML.CreateElement("CampaignId")) 
        [void] $CampaignIdNode.AppendChild($ACORD_XML.CreateTextNode($BTCode));
        }
    else {
        if ($CampaignId -ne $BTCode) {
            $CampaignId = $BTCode
        }
    }
    return $ACORD_XML
}
######################################################################################################################
Function Inject_File_AgentBookRollInfo () {
    Param(
        [Parameter(Mandatory=$true)]
        $FullPath,
        [Parameter(Mandatory=$true)]
        $BTCode
        )

    $saveFile = $false
    [xml] $XmlDocument = Get-content -Path $Fullpath
    $CampaignId = $XmlDocument.ACORD.AgentBookRollInfo.CarrierPortalInfo.CarrierInfo.CampaignId
    if ($CampaignId -eq $null) {
        $AgentBookRollInfoNode = ($XmlDocument.ACORD).AppendChild($XmlDocument.CreateElement("AgentBookRollInfo"))
        $CarrierPortalInfoNode = $AgentBookRollInfoNode.AppendChild($XmlDocument.CreateElement("CarrierPortalInfo"))
        $CarrierInfoNode = $CarrierPortalInfoNode.AppendChild($XmlDocument.CreateElement("CarrierInfo"))
        $CampaignIdNode = $CarrierInfoNode.AppendChild($XmlDocument.CreateElement("CampaignId")) 
        [void] $CampaignIdNode.AppendChild($XmlDocument.CreateTextNode($BTCode));
        $UserNameNode = $CarrierInfoNode.AppendChild($XmlDocument.CreateElement("UserName")) # this node remains empty
        $saveFile = $true
        }
    else {
        if ($CampaignId -ne $BTCode) {
            $XmlDocument.ACORD.AgentBookRollInfo.CarrierPortalInfo.CarrierInfo.CampaignId = "$BTCode"
            $saveFile = $true
        }
    }
    if ($saveFile) { $XmlDocument.Save($FullPath) }
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
######################################################################################################################
#SOURCE: https://stackoverflow.com/questions/36268925/powershell-invoke-restmethod-multipart-form-data/50255917#50255917
Function Upload_To_AQE_Using_CURL_SAVE () {
    Param(        
        [Parameter(Mandatory=$true)]
        $FolderName,   
        [Parameter(Mandatory=$true)]
        $FileName,   
        [Parameter(Mandatory=$true)]
        $BT_Code
        )

    if (-not $global:AUTOUPLOAD) { 
        Log -Str "Upload to AQE is not enabled."
        return
        }

    $Fullpath = Join-Path -Path $FolderName -ChildPath $FileName
    Inject_File_AgentBookRollInfo -FullPath $Fullpath -BTCode $BT_Code
#    $global:uploadList.Add($FileName)
    $CurlExecutable = "curl.exe"
    if ($global:PROD_UPLOAD) {
    # PRODUCTION
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices.pdc.paas.lmig.com/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "PROD"
        } else {
    # TEST
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices-staging.us-east-1.np.paas.lmig.com/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "TEST"
        }
    Log -Str "Uploading file $FileName to AQE $UploadTarget..." -SkipLog
    & $CurlExecutable @CurlArguments
    Log -Str "Done uploading file $FileName to AQE" -SkipLog

}
######################################################################################################################
Function Upload_To_AQE_Using_CURL () {
    Param(        
        [Parameter(Mandatory=$true)]
        $FolderName,   
        [Parameter(Mandatory=$true)]
        $FileName,   
        [Parameter(Mandatory=$true)]
        $BT_Code
        )

    if (-not $global:AUTOUPLOAD) { 
        Log -Str "Upload to AQE is not enabled."
        return
        }

    $Fullpath = Join-Path -Path $FolderName -ChildPath $FileName
    Inject_File_AgentBookRollInfo -FullPath $Fullpath -BTCode $BT_Code
#    $global:uploadList.Add($FileName)
    $CurlExecutable = "curl.exe"
    if ($global:PROD_UPLOAD) {
    # PRODUCTION
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices.pdc.paas.lmig.com/v2/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "PROD"
        } else {
    # TEST
        $CurlArguments = '--request', 'POST', 
                        'https://uploadservices-staging.us-east-1.np.paas.lmig.com/uploadS3',
                        '--header', "'content-type: multipart/form-data'",
                        '--form', "file=@$Fullpath"
                        '-v'
        $UploadTarget = "STAGE"
        }
    Log -Str "Uploading file $FileName to AQE $UploadTarget..."
    & $CurlExecutable @CurlArguments
    Log -Str "Sleeping for 5 seconds..."
    Start-Sleep -Seconds 5
    Log -Str "Done uploading file $FileName to AQE"

}
######################################################################################################################
######################################################################################################################
Function Merge_INMRP_With_HOME_XML () {
    Param(
        [Parameter(Mandatory=$true)]
        $BTRecord = ""
        )
    Log "Number of Inland Marine policies: $($global:InlandMarinePolicies.Count)"
    foreach ($inmrp in $global:InlandMarinePolicies) {
        $CustomerPolicies = $global:CustomerID_PolicyData[$inmrp.CustomerID]
        $MatchingHomes = $CustomerPolicies | Where-Object { ($_.LOB -eq 'HOME') -and ($_.AddrTypeCd -ne 'MailingAddress') }
        if ($MatchingHomes -eq $null) { 
            Log -Str "Inland Marine (PN: $($inmrp.PolicyNumber)) no matching Home Policy found"
            }
        else {
            if ($MatchingHomes.GetType().BaseType.Name -ne "Array") { 
                $singleMatch = $MatchingHomes
                $MatchingHomes = New-Object -TypeName System.Collections.ArrayList
                [void] $MatchingHomes.Add($singleMatch)
                }
            # We know we're going to merge so let's get the Inland Marine data ready
            $imDwells = [xml] ('<ACORD>' + $inmrp.Dwells + '</ACORD>')
            $newPS = [xml] ('<ACORD>' + $inmrp.ScheduledProperties + '</ACORD>')
            $imLocs = [xml] ('<ACORD>' + $inmrp.Locations + '</ACORD>')
            $imLocations = $imLocs.SelectSingleNode("//Location")
            # Now let's find the best matching Home/Location
            foreach ($imloc in $imLocations) {
                $imDwell = $imDwells.SelectSingleNode("//Dwell[@LocationRef='$($imloc.id)']")
                $imScheduledProperties = $newPS.SelectNodes("//PropertySchedule[@LocationRef='$($imloc.id)']")
                $IMLocKey = Addr_Key -Addr $imloc.Addr
                # Run through all the matching Home's Locations to find the best match
                $bestMatchPolicyNumber = ''
                $bestMatchLocationRef = ''
                $bestScore = 0
                foreach ($mh in $MatchingHomes) {
                    if ($IMLocKey -eq $mh.Key) {
                        # We found an exact match so we're done
                        $exactMatch = $true
                        $bestMatchPolicyNumber = $mh.PolicyNumber
                        $bestMatchLocationRef = $mh.LocationRef
                        break
                        }
                    $thisScore = Fuzzy_Match $IMLocKey $mh.Key
                    if ($thisScore -gt $bestScore) {
                        $exactMatch = $false
                        $bestScore = $thisScore
                        $bestMatchPolicyNumber = $mh.PolicyNumber
                        $bestMatchLocationRef = $mh.LocationRef
                        }
                    }
                
                # At this point we have the best matching Home Filename and LocationRef
                $bestHome = $global:HomePolicies | Where-Object { $_.PolicyNumber -eq $bestMatchPolicyNumber }
                $XmlDocument = [xml](Get-Content -Path $bestHome.Filename)
                $homeLocation = $XmlDocument.SelectSingleNode("//Location[@id='$bestMatchLocationRef']")
                if ($exactMatch) {
                    # We got an exact match so just change SP LocationRefs to the Home Location's LocationRef
                    foreach ($sp in $imScheduledProperties) { $sp.LocationRef = $bestMatchLocationRef }
                    }
                # We are now ready to proceed with the merge
                $HomeLineBusinessNode = $XmlDocument.SelectSingleNode('//HomeLineBusiness')
                $HomePolicyQuoteInqRq = $XmlDocument.SelectSingleNode('//HomePolicyQuoteInqRq')
                $DwellNode = $HomePolicyQuoteInqRq.SelectSingleNode("//Dwell[@LocationRef='$bestMatchLocationRef']")
<#                if (-not $exactMatch) {
                    # Copy over Location and Dwell nodes since it was a fuzzy match
                    $newLocationNode = $HomePolicyQuoteInqRq.AppendChild($XmlDocument.ImportNode($imLoc, $true))
                    if ($imDwell -ne $null) { $DwellNode = $HomeLineBusinessNode.AppendChild($XmlDocument.ImportNode($imDwell, $true)) }
                    else {
                        $DwellNode = $HomeLineBusinessNode.AppendChild($XmlDocument.CreateElement("Dwell"))
                        $DwellNode.SetAttribute("id", "im_D0001");
                        $DwellNode.SetAttribute("LocationRef", $imLoc.id);
                        }
                    }
                
#>
                if ($exactMatch) {
                    # Copy over Scheduled Properties
                    foreach ($prop in $imScheduledProperties) { [void] $HomeLineBusinessNode.AppendChild($XmlDocument.ImportNode($prop, $true)) }

                    # Merge Coverage node
                    $newCov = [xml] ('<ACORD>' + $inmrp.Coverage + '</ACORD>')
                    $Coverage = $newCov.SelectSingleNode('//Coverage')
                    $newCovNode = $DwellNode.AppendChild($XmlDocument.ImportNode($Coverage, $true))
                    $CovPrem = $newCovNode.CurrentTermAmt.Amt
                
                    # If the Coverage doesn't have a premium, set it to the Inland Marine Policy premium
                    if ($CovPrem -eq $null) {
                        $newCovPremText = [string] $inmrp.PolicyPremium
                        $newCovTermAmtNode = $newCovNode.AppendChild($XmlDocument.CreateElement("CurrentTermAmt"));
                        $newCovTermAmtAmt = $newCovTermAmtNode.AppendChild($XmlDocument.CreateElement("Amt"));
                        $newFullTermAmtAmtNode = $newCovTermAmtAmt.AppendChild($XmlDocument.CreateTextNode($newCovPremText));
                        }

                    # Update the Home Policy premium to add in the Scheduled Property Coverage premium
                    $PolicyPremiumNode = $XmlDocument.SelectSingleNode('//PersPolicy/CurrentTermAmt')
                    $NewPolicyPremium = ([int] $PolicyPremiumNode.Amt) + ([int] $inmrp.PolicyPremium)
                    $newPremText = [string] $NewPolicyPremium
                    $PolicyPremiumNode.Amt = $newPremText

                    # Add a Remark about the Inland Marine data being merged into the Home
                    Add_Metadata -XMLDocument $XmlDocument -NodeName 'Notice' -Value "Inland Marine (PN: $($inmrp.PolicyNumber)) merged with Home (changes: scheduled property, location, coverage, premium)."
                    # Log the matching policy
                    Log -Str "Inland Marine (PN: $($inmrp.PolicyNumber)) merged with Home (PN: $($bestMatchPolicyNumber)) (changes: scheduled property, location, coverage, premium)."
                    }
                else {
                    # Add a Remark about the matching Inland Marine but Location isn't an exact match
                    Add_Metadata -XMLDocument $XmlDocument -NodeName 'Notice' -Value "Customer also has Inland Marine (PN: $($inmrp.PolicyNumber)) but was not merged with Home because Location was not exact match."
                     # Log the matching policy
                    Log -Str "Matching Inland Marine (PN: $($inmrp.PolicyNumber)) was not merged with Home (PN: $($bestMatchPolicyNumber)) because Location was not exact match."
               }
                $XmlDocument.Save($bestHome.Filename)

                # Add a Remark in the Inland Marine policy
                $imPolicy = $global:InlandMarinePolicies | Where-Object { $_.PolicyNumber -eq $inmrp.PolicyNumber }
                $XmlDocument = [xml](Get-Content -Path $imPolicy.Filename)
                
                $RemarkText = "[Safeco BT] This policy data has been merged with Home (PN: $($bestMatchPolicyNumber))."
                if (-not $exactMatch) { $RemarkText = "[Safeco BT] This policy data was not merged with Home (PN: $($bestMatchPolicyNumber)) because Location was not exact match." }
                Add_Metadata -XMLDocument $XmlDocument -NodeName 'Notice' -Value $RemarkText
                $XmlDocument.Save($imPolicy.Filename)
                }
            # end of if ($MatchingHomes -eq $null
            }
        # end of foreach $inmrp
        }
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
Function Log() {
    Param(        
        [Parameter(Mandatory=$true)]
        $Str,
        [Parameter(Mandatory=$false)]
        $List = $null,
        [switch]
        $LineBreakBefore,
        [switch]
        $LineBreakAfter,
        [switch]
        $SkipLog
        )
    if ($global:STOPWATCH -eq $null) { $global:STOPWATCH = [system.diagnostics.stopwatch]::StartNew() }
    if ($LineBreakBefore) {if (-not $SkipLog) {[void] $global:LOG.Add("")}; write-host }
    write-host $Str
#    if (-not $SkipLog) {[void] $global:LOG.Add(($global:STOPWATCH.Elapsed.TotalSeconds.ToString("0000.00 ")) + $Str)}
    if (-not $SkipLog) {[void] $global:LOG.Add($Str)}
    if ($List -ne $null) { foreach ($s in $List) { [void] $global:LOG.Add("        * " + $s) } }
    if ($LineBreakAfter) {if (-not $SkipLog) {[void] $global:LOG.Add("")}; write-host }
}
######################################################################################################################
Function Customer_Matching_Key () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XmlDocument
        )
    $Key = $null
    $PrimaryInsured = $XmlDocument.SelectSingleNode("//InsuredOrPrincipal[InsuredOrPrincipalInfo/InsuredOrPrincipalRoleCd='Insured']")
    if ($PrimaryInsured -ne $null) {
        # NOTE: I leave out City because sometimes the same street address can have different cities and still be valid
        # NOTE: I use only first 5 digits of zip codes because the +4 may not exist or may be entered incorrectly
        $Key = ($PrimaryInsured.GeneralPartyInfo.Addr.Addr1) + ($PrimaryInsured.GeneralPartyInfo.Addr.Addr2) + `
                    ($PrimaryInsured.GeneralPartyInfo.Addr.StateProvCd) + ($PrimaryInsured.GeneralPartyInfo.Addr.PostalCode.SubString(0,5))
        $Key = $Key.ToUpper().Replace(' ','').Replace(',','').Trim()
        }
    return $Key
}
######################################################################################################################
Function Addr_Key () {
    Param(        
        [Parameter(Mandatory=$true)]
        $Addr
        )
        # NOTE: I leave out City because sometimes the same street address can have different cities and still be valid
        # NOTE: I use only first 5 digits of zip codes because the +4 may not exist or may be entered incorrectly
        try{ $Key = (($Addr.Addr1) + ($Addr.Addr2) + ($Addr.StateProvCd) + ($Addr.PostalCode.SubString(0,5))).ToUpper().Replace(' ','').Replace(',','').Trim() } catch {}
    return $Key
}
######################################################################################################################
# Inject either a new AgencyID or a matching AgencyID
Function Inject_AgencyID () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XmlDocument,
        [Parameter(Mandatory=$false)]
        $BTRecord = ""
        )
    if ($global:Key_CustomerID -eq $null) { $global:Key_CustomerID = @{} }
    if ($global:CustomerID_PolicyData -eq $null) { $global:CustomerID_PolicyData = @{} }

    $PolicyKeys = @()
    $PolicyLocations = @()
    
    $LOB = ($XmlDocument.SelectSingleNode($global:LOBCdString)).InnerXML
	$PolicyNumber = ($XmlDocument.SelectSingleNode($global:PolicyNumberString)).InnerXML
    $CommercialName = InsuredName_From_ACORD_XML $XmlDocument

    $Addrs = @()
    # First, add the MailingAddress
    $Addrs += $XmlDocument.SelectSingleNode("//Addr[AddrTypeCd='MailingAddress']")
    # Next, add all the Location Addresses
    $Addrs += $XmlDocument.SelectNodes("//Location/Addr")
    foreach ($a in $Addrs) {
        #$Addr = $l.Addr
        $Key = Addr_Key -Addr $a
        $PolicyKeys += $Key
        try {$PolicyLocations += (@{ 
			    LOB = $LOB
			    PolicyNumber = $PolicyNumber
                CommercialName = $CommercialName
                LocationRef = $a.ParentNode.id
                AddrTypeCd = $a.AddrTypeCd
                Addr1 = $a.Addr1
                Addr2 = $a.Addr2
                ST = $a.StateProvCd
                Zip = $a.PostalCode.SubString(0,5)
                Key = $Key
                }) } catch {}
        }
    # Find a matching Key or create a new one

    $CustomerID = $global:Key_CustomerID[$PolicyKeys]
    foreach ($pk in $PolicyKeys) {
        $CustomerID = $global:Key_CustomerID[$pk]
        if ($CustomerID -ne $null) { break; }
        }
    if ($CustomerID -eq $null) {
        # No match, so make a new CustomerID
        $CustomerCount = $global:CustomerID_PolicyData.Count + 1
        $CustomerID = "SBT-$BTRecord-$global:AgencyIDTimeStamp-$($CustomerCount.ToString('0000'))"
        }
    # Add all (Key,CustomerIDs) to the list
    foreach ($ppkk in $PolicyKeys) { try{ $global:Key_CustomerID += (@{ $ppkk = $CustomerID }) } catch { } }

    # Now add all (CustomerID,PolicyData) pairs to the CustomerID_PolicyData table
    if ($global:CustomerID_PolicyData[$CustomerID] -eq $null) { $global:CustomerID_PolicyData += (@{ $CustomerID = @() }) }
    foreach ($pl in $PolicyLocations) { try { $global:CustomerID_PolicyData[$CustomerID] += $pl } catch { } }

    # Now inject AgencyID into XML File (either the new one or the matching one)
    $newItemIdInfoNode = $PrimaryInsured.AppendChild($XmlDocument.CreateElement("ItemIdInfo"));
    $newAgencyIdNode = $newItemIdInfoNode.AppendChild($XmlDocument.CreateElement("AgencyId"));
    [void] $newAgencyIdNode.AppendChild($XmlDocument.CreateTextNode($CustomerID));
    # Make note that the AgencyID was generated
    Add_Metadata -XMLDocument $XmlDocument -NodeName 'Injection' -Value "Generated AgencyID: $CustomerID"
    return $XmlDocument
}
######################################################################################################################
function Add_Metadata () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XMLDocument,
        [Parameter(Mandatory=$true)]
        $NodeName,
        [Parameter(Mandatory=$true)]
        $Value
        )
    $MetaDataNode = $XmlDocument.SelectSingleNode("//$global:MetaDataNode")
    if ($MetaDataNode -eq $null) {
        # Insert a Safeco BookTransfer Metadata node
        switch (LOBCd_From_ACORD_XML ($XmlDocument)) {
            "PPKGE" { $InqRq = "PersPkgPolicyQuoteInqRq" }
            "AUTOP" { $InqRq = "PersAutoPolicyQuoteInqRq" }
            "HOME"  { $InqRq = "HomePolicyQuoteInqRq" }
            "DFIRE" { $InqRq = "DwellFirePolicyQuoteInqRq" }
            "BOAT"  { $InqRq = "WatercraftPolicyQuoteInqRq" }
            "UMBRP" { $InqRq = "PersUmbrellaPolicyQuoteInqRq" }
            "INMRP" { $InqRq = "PersInlandMarinePolicyQuoteInqRq" }
            }
        # Insert Safeco BookTransfer MetaData Node
        $InsuranceSvcRq = $XmlDocument.SelectSingleNode("//$InqRq")
        $MetaDataNode = $InsuranceSvcRq.AppendChild($XmlDocument.CreateElement($global:MetaDataNode));
    }
    $NewNode = $MetaDataNode.AppendChild($XmlDocument.CreateElement($NodeName));
    [void]$NewNode.AppendChild($XmlDocument.CreateTextNode($Value));
}
######################################################################################################################
function Generate_CustomerID_Report () {
    Param(        
        [Parameter(Mandatory=$true)]
        $XMLFolder,
        [Parameter(Mandatory=$false)]
        $BTRecord = ""
        )

    $CustomerIDReportFile = Join-Path -Path $XMLFolder -ChildPath ("CustomerID Report for BT-$BTRecord.csv")
    Add-Content -Path $CustomerIDReportFile -Value '"CustomerID","LOB","Policy Number","Commercial Name","AddrTypeCd","Addr1","Addr2","ST","Zip","Matching Key"'
    foreach ($k in ($global:CustomerID_PolicyData.keys | sort)) {
        foreach ($pol in $global:CustomerID_PolicyData[$k]) {
            # surround some fields by quotes in case there are commas
            $OutString = $k + ',' + $pol.LOB + ',' + $pol.PolicyNumber + ',' + '"' + $pol.CommercialName + '"' + ',' + $pol.AddrTypeCd + ',' + `
                '"' + $pol.Addr1 + '"' + ',' + '"' + $pol.Addr2 + '"' + ',' + $pol.ST + ',' + $pol.Zip + ',' + $pol.Key
            Add-Content -Path $CustomerIDReportFile -Value $OutString
            }
        }
}
######################################################################################################################
######################################################################################################################
######################################################################################################################
###                                                                                                                ###
###     EXECUTION                                                                                                  ###
###                                                                                                                ###
######################################################################################################################
######################################################################################################################

######################################################################################################################
cls
#Combine_XML_Files -SourceFolder "C:\Users\<USER>\Downloads\_IVANS Bookroll\Bookrolls\columbia\output" -XMLDestinationFolder "C:\Users\<USER>\Downloads\_IVANS Bookroll\Bookrolls\columbia\upload to aqe" -BTRecord "229637"

# This was experiment for Comparion
#$TheFolder = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\IVANS Bookroll\Comparion.02202025_191251\Injected Policies"
#$TheFile = "BT-421967, Comparion--Combined 1 (44 PIF).xml"
#$TheBTRecord = "421967"
#Upload_To_AQE_Using_CURL -FolderName $TheFolder -FileName $TheFile -BT_Code $TheBTRecord
#exit
#return

cls
Log -Str "  _____     ___    _   _ ____    ____              _              _ _ "
Log -Str " |_ _\ \   / / \  | \ | / ___|  | __ )  ___   ___ | | ___ __ ___ | | |"
Log -Str "  | | \ \ / / _ \ |  \| \___ \  |  _ \ / _ \ / _ \| |/ / '__/ _ \| | |"
Log -Str "  | |  \ V / ___ \| |\  |___) | | |_) | (_) | (_) |   <| | | (_) | | |"
Log -Str " |___|  \_/_/   \_\_| \_|____/  |____/ \___/ \___/|_|\_\_|  \___/|_|_|"

Log -Str "PROCESS IVANS BOOKROLL DATA, v$version" -LineBreakBefore
Log -Str $author

# Load or Set persistant data
$BOOKROLL_FOLDER = ""
$persistentVarsFile = Join-Path $PSScriptRoot "IVANS_BOOKROLL_Folder.xml"
if (Test-Path -Path $persistentVarsFile) {
    $BOOKROLL_FOLDER = Import-CliXml $persistentVarsFile
    }
if (($BOOKROLL_FOLDER -eq "") -or (-not (Test-Path -Path $BOOKROLL_FOLDER))) {
    $BOOKROLL_FOLDER = [Environment]::GetFolderPath("Desktop")
    $OpenFolderDialog = New-Object System.Windows.Forms.FolderBrowserDialog
    $OpenFolderDialog.Description = "Select the folder with IVANS zip files"
    $OpenFolderDialog.SelectedPath = ($BOOKROLL_FOLDER + "\")
    if ($OpenFolderDialog.ShowDialog() -eq "OK") {
        $BOOKROLL_FOLDER = $OpenFolderDialog.SelectedPath
        $BOOKROLL_FOLDER | Export-CliXml $persistentVarsFile
        }
    }

# $STYLESHEET_FOLDER = "C:\Users\<USER>\OneDrive - Liberty Mutual\Documents\_XML Stylesheets\Production Versions\Current Production"
$STYLESHEET_FOLDER = ""
$persistentStyleSheetFile = Join-Path $PSScriptRoot "STYLESHEETS_Folder.xml"
if (Test-Path -Path $persistentStyleSheetFile) {
    $STYLESHEET_FOLDER = Import-CliXml $persistentStyleSheetFile
    }
if (($STYLESHEET_FOLDER -eq "") -or (-not (Test-Path -Path $STYLESHEET_FOLDER))) {
    $STYLESHEET_FOLDER = [Environment]::GetFolderPath("Desktop")
    $OpenFolderDialog = New-Object System.Windows.Forms.FolderBrowserDialog
    $OpenFolderDialog.Description = "Select the folder with the StyleSheet files are kept."
    $OpenFolderDialog.SelectedPath = ($STYLESHEET_FOLDER + "\")
    if ($OpenFolderDialog.ShowDialog() -eq "OK") {
        $STYLESHEET_FOLDER = $OpenFolderDialog.SelectedPath
        $STYLESHEET_FOLDER | Export-CliXml $persistentStyleSheetFile
        }
    }

    
$Timestamp = [DateTime]::Now.ToString("yyyy-MM-dd")
$StartTime = [DateTime]::Now.ToString("yyyy-MM-dd hh:mm:ss")
Log -Str "Start: $StartTime" -LineBreakAfter

# First, check to see if there are any underscore characters at the beginning and end of the filenames
$ZipFiles = Get-ChildItem -Path $BOOKROLL_FOLDER -Filter "_*.zip_" -File
Foreach ($zip in $ZipFiles) {
    # if so, rename them by removing the first and last underscore character
    Rename-Item -Path $zip.Fullname -NewName $zip.Name.Substring(1, $zip.Name.Length-2)
    Log -Str "Renamed zip file '$($zip.Name)' by removing underscore characters at beginning and end of filename"
    }

$OriginalFolderName = $Timestamp + " (Original)"
$ZipFiles = Get-ChildItem -Path $BOOKROLL_FOLDER -Filter "*.zip" -File
$Missing_BTRecords = ""
$totalZips = $ZipFiles.Count

Log -Str "Zip File Folder: $BOOKROLL_FOLDER" -LineBreakBefore
$plural = if ($totalZips -ne 1) {'s'} else {''}

if ($totalZips -gt 0) {
    Log -Str "Processing $totalZips zip file$plural."
    $result = Read-Host "Ready to process $totalZips zip file$plural.`n`n(Press ENTER to continue)"
    
    $TrashCanFolder = Join-Path -Path $BOOKROLL_FOLDER -ChildPath "ZIPS_TO_DELETE"
    if (!(Test-Path -Path $TrashCanFolder)) { [void] (New-Item -Path $TrashCanFolder -ItemType "Directory") } 

    $TempFolder = Join-Path -Path $BOOKROLL_FOLDER -ChildPath "ZIPS_$Timestamp"
    if (!(Test-Path -Path $TempFolder)) { [void] (New-Item -Path $TempFolder -ItemType "Directory") }       
    Foreach ($zip in $ZipFiles) {
        $fname = $zip.Basename
        if ($fname.SubString(0,6) -eq "Files_") {
            Log -Str "$fname --- This zip contains more zips. Unzipping..."
            Expand-Archive -LiteralPath $zip.FullName -DestinationPath $TempFolder
            Move-Item -Path $zip.FullName -Destination $TrashCanFolder
            }
        else {
            Move-Item -Path $zip.FullName -Destination $TempFolder
            }
        }

    $ZipFiles = Get-ChildItem -Path $TempFolder -Filter "*.zip" -File
    $totalZips = $ZipFiles.Count
    $plural = if ($totalZips -ne 1) {'s'} else {''}
    $isOrare = if ($totalZips -ne 1) {'are'} else {'is'}
    Log -Str "There $isOrare $totalZips zip file$plural to process."
    $z = 0
    Foreach ($zip in $ZipFiles) {
        $folderName = ($zip.Basename).Replace(",","")
        $AgencyFolder = Join-Path -Path $BOOKROLL_FOLDER -ChildPath $folderName
        if (!(Test-Path -Path $AgencyFolder)) { [void](New-Item -Path $AgencyFolder -ItemType "Directory") }
        $OriginalFolder = Join-Path -Path $AgencyFolder -ChildPath $OriginalFolderName
        if (!(Test-Path -Path $OriginalFolder)) { [void](New-Item -Path $OriginalFolder -ItemType "Directory") }
        Expand-Archive -LiteralPath $zip.FullName -DestinationPath $OriginalFolder
        Move-Item -Path $zip.FullName -Destination $OriginalFolder
        $BT_Record = ""
        $HeaderSummary = Load_HeaderSummary_File $OriginalFolder
        $MetaData = $HeaderSummary.BookrollRequestPoliciesSummary
        if ($MetaData -eq $null) { $MetaData = $HeaderSummary.ExpandedBookrollRequestPoliciesSummary }
        $AgencyName = $MetaData.AgencyName
        $AgencyAccount = $MetaData.AgencyAccount
        $TransferCode = $MetaData.TransferCode
        if ($TransferCode -ne $null) { $BT_Record = $TransferCode.Replace("BT","").Replace("-","").Trim() }
        if ($BT_Record -ne "") { 
            $IVANS_XML_SOURCE = $OriginalFolder
            $IVANS_XML_DESTINATION = Join-Path -Path $AgencyFolder -ChildPath ($Timestamp + " (Processed XML)")
            $IVANS_XML_UPLOAD = Join-Path -Path $AgencyFolder -ChildPath ($Timestamp + " (To Upload)")
            Log -Str "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++" -LineBreakBefore
            Log -Str "Processing XML for Agency: $AgencyName, BT-$BT_Record" 
            
            Log -Str "Preparing individual XML files..." -SkipLog
            # Re-initialize global variables for the next set of policies
            $global:InlandMarinePolicies = @()
            $global:HomePolicies = @()
            $global:Key_CustomerID = $null
            $global:CustomerID_PolicyData = $null
            [System.GC]::Collect()

            $clear = $global:Counts.GetEnumerator() | ? {$_.Value -ne 0}
            $clear | % { $global:Counts[$_.Key]=0 }
            
            # reset AgencyID timestamp for each book
            $global:AgencyIDTimeStamp = [DateTime]::Now.ToString("hhmmss") 

            Process_IVANS_Bookroll_XML -XMLFolder $IVANS_XML_SOURCE -DestinationFolder $IVANS_XML_DESTINATION -BT_Record $BT_Record
            
            if ($global:Counts['InvalidTaxIds'] -gt 0) { Log -Str "Removed $($global:Counts['InvalidTaxIds']) invalid TaxIds." }
            if ($global:Counts['PersPolicyAdditionalInterests'] -gt 0) { Log -Str "Moved $($global:Counts['PersPolicyAdditionalInterests']) AdditionalInterests from PersPolicy to Location." }
            if ($global:Counts['PackagedPolicies'] -gt 0) { Log -Str "Split $($global:Counts['PackagedPolicies']) Packaged policies." }
            
            Copy_StyleSheets -SourceFolder $STYLESHEET_FOLDER -DestinationFolder $IVANS_XML_DESTINATION       
            Log -Str "END preparing individual XML files." -SkipLog

            Log -Str "Merging Inland Marine with Home policies..." -SkipLog
            Merge_INMRP_With_HOME_XML -BTRecord $BT_Record
            Log -Str "END merging Inland Marine with Home policies." -SkipLog

            if ($global:FLAG_Generate_CustomerID_Report) { Generate_CustomerID_Report -XMLFolder $IVANS_XML_DESTINATION -BTRecord $BT_Record }
            $totalPolicies = (Get-ChildItem -Path $IVANS_XML_DESTINATION -Filter *.xml -File).count
            Log -Str "Total number of AgencyIDs generated: $($global:CustomerID_PolicyData.Count) for $totalPolicies policies."
            
            if ($global:COMBINE_FILES) {
                Log -Str "Combining XML files..." -SkipLog
                Combine_XML_Files -SourceFolder $IVANS_XML_DESTINATION -XMLDestinationFolder $IVANS_XML_UPLOAD -BTRecord $BT_Record
                Log -Str "END combining XML files." -SkipLog
                }
            else {
                Log -Str "SKIPPING Combining XML files..." -SkipLog
                }

            Log -Str "END processing XML for Agency: $AgencyName, BT-$BT_Record" -SkipLog
            Log -Str "+++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++"
            }
        else { $Missing_BTRecords = $Missing_BTRecords + " " + $AgencyAccount; }
        $z++
        Log -Str "$z/$totalZips zip file$plural processed"
        }
        
        Remove-Item $TempFolder

        if ($Missing_BTRecords -ne "") { Log -Str "No BT Record found for agency accounts: $Missing_BTRecords." }

        $uploadCount = $global:uploadList.Count
        Log -Str "Uploaded $uploadCount files to AQE."
#        $UploadFileName = Join-Path -Path $BOOKROLL_FOLDER -ChildPath "Files Uploaded to AQE - $Timestamp.txt"
#        $global:uploadList | Out-File -FilePath $UploadFileName

        Log -Str "  ____   ___  _   _ _____ _ " -LineBreakBefore
        Log -Str " |  _ \ / _ \| \ | | ____| |"
        Log -Str " | | | | | | |  \| |  _| | |"
        Log -Str " | |_| | |_| | |\  | |___|_|"
        Log -Str " |____/ \___/|_| \_|_____(_)"
        Log -Str "Done processing $totalZips zip file$plural." -LineBreakBefore

        $result = Read-Host "Press ENTER to close this window"

        $LogFileName = Join-Path -Path $BOOKROLL_FOLDER -ChildPath "Process IVANS Bookroll Log - $Timestamp.txt"
        # Save the log file as .txt
        $global:Log | Out-File -FilePath $LogFileName
        if ($global:STOPWATCH -ne $null) { $global:STOPWATCH.Stop(); $global:STOPWATCH = $null }
        Start-Process -FilePath $LogFileName
    } else {
        write-host ""
        $result = Read-Host "Sorry, but there are no zip files found. Please be sure you've placed them in the zip file folder: `n$BOOKROLL_FOLDER `n`n(Press ENTER to close this window)"
        }



                                



        # TEST
<#
$xmlfile = Join-Path -Path "C:\Users\<USER>\Downloads\_IVANS Bookroll\Bookrolls\name fix" -ChildPath "df5fe440-e89e-456d-b5e5-25e491748eb0.xml"       
$RawXMLText = Get-content $xmlfile -raw
$XmlDocument = [xml]$RawXMLText
$XmlDocument = Distribute_CommercialName $XmlDocument
$XmlDocument = Check_And_Adjust_Names $XmlDocument
#>

