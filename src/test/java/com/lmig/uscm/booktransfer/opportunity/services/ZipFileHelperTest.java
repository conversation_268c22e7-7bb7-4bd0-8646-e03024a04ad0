package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.domain.creation.FileContent;
import com.lmig.uscm.booktransfer.utilityservice.xml.XmlHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ZipFileHelperTest {

	private static final String ZIP_OUT_PATH = "src/test/resources/xml/multiCompressed.zip";

	@AfterEach
	public void cleanup() {
		File zipFile = new File(ZIP_OUT_PATH);
		zipFile.delete();
	}

	@Test
	void testIsArchive() throws Exception {
		String normalFilePath = "src/test/resources/xml/auto12Vehs.xml";
		String archiveFilePath = "src/test/resources/xml/testArchiveDeep.zip";

		String archiveWithoutExtension = "src/test/resources/xml/testArchive";

		ZipFileHelper.createZipFile(archiveWithoutExtension, normalFilePath);

		File archiveFile = new File(archiveFilePath);
		File normalFile = new File(normalFilePath);
		File noExtensionFile = new File(archiveWithoutExtension);

		assertFalse(ZipFileHelper.isArchive(normalFile));
		assertTrue(ZipFileHelper.isArchive(archiveFile));
		assertTrue(ZipFileHelper.isArchive(noExtensionFile));

		noExtensionFile.delete();
	}

	@Test
	void testUnzipDeepAndShallowFiles() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String archiveDeep = "src/test/resources/xml/testArchiveDeep.zip";
		String archiveShallow = "src/test/resources/xml/testArchiveShallow.zip";

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(archiveShallow);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("testArchiveShallow/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("testArchiveShallow/home.xml", unzippedFiles.get(1).getFileName());

		zipFile = new File(archiveDeep);

		unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("SomeFolder/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("SomeFolder/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testZipAndUnzipFile() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		assertEquals(file1Content, unzippedFiles.get(0).getContent());
		assertEquals("multiCompressed/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals(file2Content, unzippedFiles.get(1).getContent());
		assertEquals("multiCompressed/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testReadFileContents_ZipOrNotZip() throws Exception {
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		ZipFileHelper.createZipFile(ZIP_OUT_PATH, filePaths);

		String file1Content = XmlHelper.getFileContentFromPath(file1);
		String file2Content = XmlHelper.getFileContentFromPath(file2);

		File zipFile = new File(ZIP_OUT_PATH);

		List<FileContent> archiveFileContents = ZipFileHelper.readFileContents(zipFile);

		assertEquals(file1Content, archiveFileContents.get(0).getContent());
		assertEquals("multiCompressed/auto12Vehs.xml", archiveFileContents.get(0).getFileName());
		assertEquals(file2Content, archiveFileContents.get(1).getContent());
		assertEquals("multiCompressed/home.xml", archiveFileContents.get(1).getFileName());

		File notZipFile = new File(file1);

		List<FileContent> fileContents = ZipFileHelper.readFileContents(notZipFile);

		assertEquals(file1Content, fileContents.get(0).getContent());
		assertEquals("auto12Vehs.xml", fileContents.get(0).getFileName());
	}

	@Test
	void testUnzipFile_WithoutFolderStructure_ShouldAddZipNameAsFolder() throws Exception {

		String archiveShallow = "src/test/resources/xml/testArchiveShallow.zip";
		File zipFile = new File(archiveShallow);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files without folder structure get the zip name as folder prefix
		assertEquals("testArchiveShallow/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("testArchiveShallow/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testUnzipFile_WithFolderStructure_ShouldPreserveOriginalPath() throws Exception {
		// Test that files with existing folder structure are preserved
		String archiveDeep = "src/test/resources/xml/testArchiveDeep.zip";
		File zipFile = new File(archiveDeep);

		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files with existing folder structure are preserved as-is
		assertEquals("SomeFolder/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("SomeFolder/home.xml", unzippedFiles.get(1).getFileName());
	}

	@Test
	void testUnzipFile_WithoutFileStructureScenario() throws Exception {
		// Create a test zip file that simulates the "Main Street America.zip" scenario
		String file1 = "src/test/resources/xml/auto12Vehs.xml";
		String file2 = "src/test/resources/xml/home.xml";
		String[] filePaths = new String[]{file1, file2};
		String mainStreetZipPath = "src/test/resources/xml/Main Street America.zip";

		ZipFileHelper.createZipFile(mainStreetZipPath, filePaths);

		File zipFile = new File(mainStreetZipPath);
		List<FileContent> unzippedFiles = ZipFileHelper.unzipFile(zipFile);

		// Verify that files get the zip name as folder prefix (simulating the expected behavior)
		assertEquals("Main Street America/auto12Vehs.xml", unzippedFiles.get(0).getFileName());
		assertEquals("Main Street America/home.xml", unzippedFiles.get(1).getFileName());

		// Cleanup
		zipFile.delete();
	}
}
