<ACORD>
    <SignonRq>
        <SignonPswd>
            <CustId>
                <SPName>com.agencyport</SPName>
                <CustLoginId>default</CustLoginId>
            </CustId>
            <CustPswd>
                <EncryptionTypeCd>NONE</EncryptionTypeCd>
                <Pswd>default</Pswd>
            </CustPswd>
        </SignonPswd>
        <ClientDt>2014-10-03T13:41:00</ClientDt>
        <CustLangPref>en-US</CustLangPref>
        <ClientApp>
            <Org>BookSmarty</Org>
            <Name>AgencyPort</Name>
            <Version>1.1.0.1</Version>
        </ClientApp>
    </SignonRq>
    <InsuranceSvcRq>
        <RqUID>D5A5F71C-DB12-814C-2D84-B9234A21F449</RqUID>
        <HomePolicyQuoteInqRq>
            <RqUID>E638730C-988D-7E1E-D2B5-8CDE1E17408E</RqUID>
            <TransactionRequestDt id="A81FA81167A19F32FB49CA856B31B74BFA">2014-10-03</TransactionRequestDt>
            <TransactionEffectiveDt id="A7EE59DB9D229D53919E2CD6E526BAF4DA">2014-10-03</TransactionEffectiveDt>
            <CurCd id="A3CC4F7CD1C060064948440E228225E6AA">USD</CurCd>
            <com.safeco_TransactionType id="AB6BD47476AD5ECFADE609D114B3724E8A">QuoteRQ</com.safeco_TransactionType>
            <Producer id="A7631F515100647C9876164E87A95AFB8A">
                <GeneralPartyInfo id="AE844210D7732A3CB1A59E026E7E2F2DAA">
                    <NameInfo id="AC6A4764706E93461585D1EBE26EE0061A">
                        <CommlName id="A342DA21A65C4BDB76C7C1C91B776A155A">
                            <CommercialName id="A8EE87F8F93D641909B79DCE323106686A">Knoxville West, TN Branch
                            </CommercialName>
                        </CommlName>
                    </NameInfo>
                    <Addr id="AF3DBA146E1452078A93E01CC4493CEDEA">
                        <Addr1 id="A55DD88671B0BC59DD16D0111DB58A15FA">110 Capital Drive</Addr1>
                        <City id="AED327339FFA31ADCCD54258F172159F0A">Knoxville</City>
                        <StateProvCd id="AC2D91294B5CA346DAA689B8E51CA3032A">TN</StateProvCd>
                        <PostalCode id="A63AA4FA0FD4F301419AE74D39718D0F3A">37922</PostalCode>
                    </Addr>
                    <Communications id="AD1CC223CA31E3248306E9685AC5BAE78A">
                        <PhoneInfo id="AA5AAD5D5BDB7AE548E36767F81AF56E8A">
                            <PhoneTypeCd id="A9CF42E7A4767D8DDED5AF86182CE7A8BA">Phone</PhoneTypeCd>
                            <CommunicationUseCd id="A54AA682521306BDBF66006A0E21CB32AA">Business</CommunicationUseCd>
                            <PhoneNumber id="A270E0605DD02A0013359BBBF8F52A241A">******-6371910</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo id="A0F92F263540E0EAA8580FB60B0BC14ADA">
                            <PhoneTypeCd id="A315A7B0873D13991C5E75DE9D03698C8A">Phone</PhoneTypeCd>
                            <CommunicationUseCd id="A8A062B00E6DF572E4060B462DC62459CA">Business</CommunicationUseCd>
                            <PhoneNumber id="AD34774ABD6AFD9C96490A25758EF2A9AA">******-8629020</PhoneNumber>
                        </PhoneInfo>
                        <PhoneInfo id="A306F705380AA17D2734E562FD3E363A7A">
                            <PhoneTypeCd id="AB677565ADB68A7099B4D52D66EF8E297A">Fax</PhoneTypeCd>
                            <PhoneNumber id="A46AB96D7B7379FB84898C106720433F5A">******--691-10+54</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo id="AD031F342DA0BD996B60F4F736760B39CA">
                            <CommunicationUseCd id="ADBF94BEB0086301C1D792BE42F827938A">Business</CommunicationUseCd>
                            <EmailAddr id="AEAC59228A673DFBFDC199BC3FFDC5CF7A"><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <ProducerInfo id="A7269CE98038FC6C082F84C6C5B52DE02A">
                    <ProducerSubCode id="A467294984CFB7C7C923E27D2B902B802A">Book Transfer</ProducerSubCode>
                    <ProducerRoleCd id="AB325751A56155EDDA86A6FC2FCAD5A53A">Agency</ProducerRoleCd>
                </ProducerInfo>
            </Producer>
            <InsuredOrPrincipal id="A4457A2AF6B08A7995301AE6A9CD703C7A">
                <ItemIdInfo id="A8C7C25FBD4C66CFF69CB5006D1F80FC1A">
                    <AgencyId id="A542DD9E7F54D8AF45BA9F84C7D57DB1BA">LIUCUIXIAN</AgencyId>
                </ItemIdInfo>
                <GeneralPartyInfo id="A3799E63A015FA0A940B5AA16D1005133A">
                    <NameInfo id="A08CA93129FBD2C9624ED4B2B7F173BF3A">
                        <CommlName>
                            <CommercialName>Robert Davis and Susan Williams</CommercialName>
                        </CommlName>
                        <TaxIdentity id="A70EAE8FD5686D84897382D8D40BF3B84A">
                            <TaxIdTypeCd id="A6DE2EDCFB14FCB171942CE1EEAEEAA6FA">SSN</TaxIdTypeCd>
                            <TaxId id="A8DE61D7825E028811F09CAA74C1CAD3DA">***********</TaxId>
                        </TaxIdentity>
                    </NameInfo>
                    <Addr id="A06DCF9704162B427DDFD0611F0A33473A">
                        <AddrTypeCd id="A3AEF7C5A66FDF1AEF79181BCA02CEBDFA">MailingAddress</AddrTypeCd>
                        <Addr1 id="A14D263C06820220BCBF6AD029919B109A">12924 LONG RIDGE RD</Addr1>
                        <City id="AFE979539681CC0DA69A59453997EFF5BA">KNOXVILLE</City>
                        <StateProvCd id="AC54BF88B444B8B5E9EAA2F98FF16978CA">TN</StateProvCd>
                        <PostalCode id="A301FC1CF10E0242FB96761A0413E1F54A">37934</PostalCode>
                    </Addr>
                    <Communications id="ADF9C4046E4A841F19BF5D74FE6B909F3A">
                        <PhoneInfo id="AC0CB8A418F62658B9C691EB131ED84F2A">
                            <PhoneTypeCd id="AE6D55EFB2F9A8E84AAC0C70F437D1D98A">Phone</PhoneTypeCd>
                            <CommunicationUseCd id="ABE2B8B9B249D3203560ABF03DDC563D5A">Home</CommunicationUseCd>
                            <PhoneNumber id="A40AEC8C259B0F856824B2F2E81DD21F4A">************</PhoneNumber>
                        </PhoneInfo>
                        <EmailInfo id="A37BABC04548E6608F6C0C375A5212E86A">
                            <CommunicationUseCd id="A6305B36FAEA076AE99E37164CDDD28C6A">Business</CommunicationUseCd>
                            <EmailAddr id="A0EBCF707F6E6688B723B9E56DC5FD922A"><EMAIL></EmailAddr>
                        </EmailInfo>
                    </Communications>
                </GeneralPartyInfo>
                <InsuredOrPrincipalInfo id="AA01B15170659100F6F934774971E795EA">
                    <InsuredOrPrincipalRoleCd id="A38B7194A8EC1855D44EE781BE883705AA">Insured</InsuredOrPrincipalRoleCd>
                    <PersonInfo id="AB5DC1CA139FFB3919481C4E5EB4A244DA">
                        <GenderCd id="A94B11C2349E564DDD5A00FDA41FF152FA">Male</GenderCd>
                        <BirthDt id="A82CC44B4A272FF2EDD79325293D793F9A">1989-09-09</BirthDt>
                        <com.safeco_IndustryCd id="A2D545753AD9506595DF9646FF37AC429A">Other</com.safeco_IndustryCd>
                        <com.safeco_OccupationCd id="A6E8CA07ED5B4E2CB10EC56D589288556A">Sales</com.safeco_OccupationCd>
                    </PersonInfo>
                </InsuredOrPrincipalInfo>
            </InsuredOrPrincipal>
            <!-- NO CO-INSURED NODE EXISTS - This is what we want to test -->
            <PersPolicy id="A0643562A2AA76475338014C9241FD57DA">
                <PolicyNumber id="ADE60C921A43C6C883BF4127544906782A">ACH3000578867</PolicyNumber>
                <LOBCd id="A613E57B40942CFACE33304A92E52C464A">HOME</LOBCd>
                <NAICCd id="ACFF1A5CE06177E26F7548DF9192ADD11A">19941</NAICCd>
                <ControllingStateProvCd id="AD6D48A5AD246F0D71628D8B294587435A">TN</ControllingStateProvCd>
                <ContractTerm id="A407692E7EC6E93E69E36198BF1357955A">
                    <EffectiveDt id="A92B283D5E5F4045DA2E013FBA3627B3BA">2014-10-27</EffectiveDt>
                    <ExpirationDt id="A6D956EBAA4098BE6B9E4A40ED08A18DEA">2014-10-27</ExpirationDt>
                    <DurationPeriod id="A8767A85B0F72D113BD99B1EFAC676981A">
                        <NumUnits id="ADD0554B5B817C52DB19A687E4501B99AA">12</NumUnits>
                    </DurationPeriod>
                </ContractTerm>
                <BillingMethodCd id="A68656B574F689B9778EB812EE171FD66A">CAB</BillingMethodCd>
                <OriginalInceptionDt id="AD18B139ED9E87FA91C1071654A55FB36A">2013-10-14</OriginalInceptionDt>
            </PersPolicy>
        </HomePolicyQuoteInqRq>
    </InsuranceSvcRq>
</ACORD>
